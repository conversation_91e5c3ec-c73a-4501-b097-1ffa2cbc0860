PODS:
  - abseil/algorithm (1.20240722.0):
    - abseil/algorithm/algorithm (= 1.20240722.0)
    - abseil/algorithm/container (= 1.20240722.0)
  - abseil/algorithm/algorithm (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240722.0):
    - abseil/base/atomic_hook (= 1.20240722.0)
    - abseil/base/base (= 1.20240722.0)
    - abseil/base/base_internal (= 1.20240722.0)
    - abseil/base/config (= 1.20240722.0)
    - abseil/base/core_headers (= 1.20240722.0)
    - abseil/base/cycleclock_internal (= 1.20240722.0)
    - abseil/base/dynamic_annotations (= 1.20240722.0)
    - abseil/base/endian (= 1.20240722.0)
    - abseil/base/errno_saver (= 1.20240722.0)
    - abseil/base/fast_type_id (= 1.20240722.0)
    - abseil/base/log_severity (= 1.20240722.0)
    - abseil/base/malloc_internal (= 1.20240722.0)
    - abseil/base/no_destructor (= 1.20240722.0)
    - abseil/base/nullability (= 1.20240722.0)
    - abseil/base/poison (= 1.20240722.0)
    - abseil/base/prefetch (= 1.20240722.0)
    - abseil/base/pretty_function (= 1.20240722.0)
    - abseil/base/raw_logging_internal (= 1.20240722.0)
    - abseil/base/spinlock_wait (= 1.20240722.0)
    - abseil/base/strerror (= 1.20240722.0)
    - abseil/base/throw_delegate (= 1.20240722.0)
  - abseil/base/atomic_hook (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240722.0):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/poison (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240722.0):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240722.0):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240722.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240722.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_map
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240722.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_container_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hash_container_defaults (1.20240722.0):
    - abseil/base/config
    - abseil/container/hash_function_defaults
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240722.0):
    - abseil/base/config
    - abseil/container/common
    - abseil/hash/hash
    - abseil/meta/type_traits
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240722.0):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240722.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/bounded_utf8_length_sequence (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/decode_rust_punycode (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/debugging/bounded_utf8_length_sequence
    - abseil/debugging/utf8_for_code_point
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/debugging/demangle_rust
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/debugging/demangle_rust (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/decode_rust_punycode
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/debugging/utf8_for_code_point (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/commandlineflag
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240722.0):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240722.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240722.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.20240722.0):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.20240722.0):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.20240722.0):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.20240722.0):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.20240722.0):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.20240722.0):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.20240722.0):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.20240722.0):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.20240722.0):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.20240722.0):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.20240722.0):
    - abseil/memory/memory (= 1.20240722.0)
  - abseil/memory/memory (1.20240722.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240722.0):
    - abseil/meta/type_traits (= 1.20240722.0)
  - abseil/meta/type_traits (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/types/compare
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240722.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240722.0):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240722.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240722.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240722.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240722.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240722.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240722.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240722.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240722.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240722.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240722.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240722.0):
    - abseil/base/config
    - abseil/base/nullability
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240722.0):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/compare
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240722.0):
    - abseil/base/config
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240722.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240722.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240722.0):
    - abseil/time/internal (= 1.20240722.0)
    - abseil/time/time (= 1.20240722.0)
  - abseil/time/internal (1.20240722.0):
    - abseil/time/internal/cctz (= 1.20240722.0)
  - abseil/time/internal/cctz (1.20240722.0):
    - abseil/time/internal/cctz/civil_time (= 1.20240722.0)
    - abseil/time/internal/cctz/time_zone (= 1.20240722.0)
  - abseil/time/internal/cctz/civil_time (1.20240722.0):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240722.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240722.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240722.0):
    - abseil/types/any (= 1.20240722.0)
    - abseil/types/bad_any_cast (= 1.20240722.0)
    - abseil/types/bad_any_cast_impl (= 1.20240722.0)
    - abseil/types/bad_optional_access (= 1.20240722.0)
    - abseil/types/bad_variant_access (= 1.20240722.0)
    - abseil/types/compare (= 1.20240722.0)
    - abseil/types/optional (= 1.20240722.0)
    - abseil/types/span (= 1.20240722.0)
    - abseil/types/variant (= 1.20240722.0)
  - abseil/types/any (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240722.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240722.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240722.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240722.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240722.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240722.0)
  - agora_rtc_engine (6.5.2):
    - AgoraIrisRTC_iOS (= 4.5.2-build.1)
    - AgoraRtcEngine_iOS (= 4.5.2)
    - Flutter
  - AgoraInfra_iOS (********)
  - AgoraIrisRTC_iOS (4.5.2-build.1)
  - AgoraRtcEngine_iOS (4.5.2):
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.2)
    - AgoraRtcEngine_iOS/AIAECLL (= 4.5.2)
    - AgoraRtcEngine_iOS/AINS (= 4.5.2)
    - AgoraRtcEngine_iOS/AINSLL (= 4.5.2)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.5.2)
    - AgoraRtcEngine_iOS/ClearVision (= 4.5.2)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.5.2)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.5.2)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.5.2)
    - AgoraRtcEngine_iOS/LipSync (= 4.5.2)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.5.2)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.2)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.2)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.5.2)
    - AgoraRtcEngine_iOS/VQA (= 4.5.2)
  - AgoraRtcEngine_iOS/AIAEC (4.5.2)
  - AgoraRtcEngine_iOS/AIAECLL (4.5.2)
  - AgoraRtcEngine_iOS/AINS (4.5.2)
  - AgoraRtcEngine_iOS/AINSLL (4.5.2)
  - AgoraRtcEngine_iOS/AudioBeauty (4.5.2)
  - AgoraRtcEngine_iOS/ClearVision (4.5.2)
  - AgoraRtcEngine_iOS/ContentInspect (4.5.2)
  - AgoraRtcEngine_iOS/FaceCapture (4.5.2)
  - AgoraRtcEngine_iOS/FaceDetection (4.5.2)
  - AgoraRtcEngine_iOS/LipSync (4.5.2)
  - AgoraRtcEngine_iOS/ReplayKit (4.5.2)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.2):
    - AgoraInfra_iOS (= ********)
  - AgoraRtcEngine_iOS/SpatialAudio (4.5.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.5.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.5.2)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.2)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.2)
  - AgoraRtcEngine_iOS/VirtualBackground (4.5.2)
  - AgoraRtcEngine_iOS/VQA (4.5.2)
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audio_session (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
  - BoringSSL-GRPC (0.0.37):
    - BoringSSL-GRPC/Implementation (= 0.0.37)
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Implementation (0.0.37):
    - BoringSSL-GRPC/Interface (= 0.0.37)
  - BoringSSL-GRPC/Interface (0.0.37)
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (5.6.7):
    - Firebase/Firestore (= 11.10.0)
    - firebase_core
    - Flutter
  - cloud_functions (5.5.1):
    - Firebase/Functions (= 11.10.0)
    - firebase_core
    - Flutter
  - CryptoSwift (1.8.4)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Firestore (11.10.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.10.0)
  - Firebase/Functions (11.10.0):
    - Firebase/CoreOnly
    - FirebaseFunctions (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - Firebase/Storage (11.10.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.10.0)
  - firebase_app_check (0.3.2-6):
    - Firebase/CoreOnly (~> 11.10.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.10.0)
    - Flutter
  - firebase_auth (5.5.3):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.5):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_storage (12.4.5):
    - Firebase/Storage (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheck (11.10.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
  - FirebaseAppCheckInterop (11.12.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.12.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseFirestore (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseFirestoreInternal (= 11.10.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.10.0):
    - abseil/algorithm (~> 1.20240722.0)
    - abseil/base (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/memory (~> 1.20240722.0)
    - abseil/meta (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/time (~> 1.20240722.0)
    - abseil/types (~> 1.20240722.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - "gRPC-C++ (~> 1.69.0)"
    - gRPC-Core (~> 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseFunctions (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseMessagingInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseMessagingInterop (11.15.0)
  - FirebaseSharedSwift (11.12.0)
  - FirebaseStorage (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - Flutter (1.0.0)
  - flutter_callkit_incoming (0.0.1):
    - CryptoSwift
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.69.0)":
    - "gRPC-C++/Implementation (= 1.69.0)"
    - "gRPC-C++/Interface (= 1.69.0)"
  - "gRPC-C++/Implementation (1.69.0)":
    - abseil/algorithm/container (~> 1.20240722.0)
    - abseil/base/base (~> 1.20240722.0)
    - abseil/base/config (~> 1.20240722.0)
    - abseil/base/core_headers (~> 1.20240722.0)
    - abseil/base/log_severity (~> 1.20240722.0)
    - abseil/base/no_destructor (~> 1.20240722.0)
    - abseil/cleanup/cleanup (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/container/flat_hash_set (~> 1.20240722.0)
    - abseil/container/inlined_vector (~> 1.20240722.0)
    - abseil/flags/flag (~> 1.20240722.0)
    - abseil/flags/marshalling (~> 1.20240722.0)
    - abseil/functional/any_invocable (~> 1.20240722.0)
    - abseil/functional/bind_front (~> 1.20240722.0)
    - abseil/functional/function_ref (~> 1.20240722.0)
    - abseil/hash/hash (~> 1.20240722.0)
    - abseil/log/absl_check (~> 1.20240722.0)
    - abseil/log/absl_log (~> 1.20240722.0)
    - abseil/log/check (~> 1.20240722.0)
    - abseil/log/globals (~> 1.20240722.0)
    - abseil/log/log (~> 1.20240722.0)
    - abseil/memory/memory (~> 1.20240722.0)
    - abseil/meta/type_traits (~> 1.20240722.0)
    - abseil/numeric/bits (~> 1.20240722.0)
    - abseil/random/bit_gen_ref (~> 1.20240722.0)
    - abseil/random/distributions (~> 1.20240722.0)
    - abseil/random/random (~> 1.20240722.0)
    - abseil/status/status (~> 1.20240722.0)
    - abseil/status/statusor (~> 1.20240722.0)
    - abseil/strings/cord (~> 1.20240722.0)
    - abseil/strings/str_format (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/synchronization/synchronization (~> 1.20240722.0)
    - abseil/time/time (~> 1.20240722.0)
    - abseil/types/optional (~> 1.20240722.0)
    - abseil/types/span (~> 1.20240722.0)
    - abseil/types/variant (~> 1.20240722.0)
    - abseil/utility/utility (~> 1.20240722.0)
    - "gRPC-C++/Interface (= 1.69.0)"
    - "gRPC-C++/Privacy (= 1.69.0)"
    - gRPC-Core (= 1.69.0)
  - "gRPC-C++/Interface (1.69.0)"
  - "gRPC-C++/Privacy (1.69.0)"
  - gRPC-Core (1.69.0):
    - gRPC-Core/Implementation (= 1.69.0)
    - gRPC-Core/Interface (= 1.69.0)
  - gRPC-Core/Implementation (1.69.0):
    - abseil/algorithm/container (~> 1.20240722.0)
    - abseil/base/base (~> 1.20240722.0)
    - abseil/base/config (~> 1.20240722.0)
    - abseil/base/core_headers (~> 1.20240722.0)
    - abseil/base/log_severity (~> 1.20240722.0)
    - abseil/base/no_destructor (~> 1.20240722.0)
    - abseil/cleanup/cleanup (~> 1.20240722.0)
    - abseil/container/flat_hash_map (~> 1.20240722.0)
    - abseil/container/flat_hash_set (~> 1.20240722.0)
    - abseil/container/inlined_vector (~> 1.20240722.0)
    - abseil/flags/flag (~> 1.20240722.0)
    - abseil/flags/marshalling (~> 1.20240722.0)
    - abseil/functional/any_invocable (~> 1.20240722.0)
    - abseil/functional/bind_front (~> 1.20240722.0)
    - abseil/functional/function_ref (~> 1.20240722.0)
    - abseil/hash/hash (~> 1.20240722.0)
    - abseil/log/check (~> 1.20240722.0)
    - abseil/log/globals (~> 1.20240722.0)
    - abseil/log/log (~> 1.20240722.0)
    - abseil/memory/memory (~> 1.20240722.0)
    - abseil/meta/type_traits (~> 1.20240722.0)
    - abseil/numeric/bits (~> 1.20240722.0)
    - abseil/random/bit_gen_ref (~> 1.20240722.0)
    - abseil/random/distributions (~> 1.20240722.0)
    - abseil/random/random (~> 1.20240722.0)
    - abseil/status/status (~> 1.20240722.0)
    - abseil/status/statusor (~> 1.20240722.0)
    - abseil/strings/cord (~> 1.20240722.0)
    - abseil/strings/str_format (~> 1.20240722.0)
    - abseil/strings/strings (~> 1.20240722.0)
    - abseil/synchronization/synchronization (~> 1.20240722.0)
    - abseil/time/time (~> 1.20240722.0)
    - abseil/types/optional (~> 1.20240722.0)
    - abseil/types/span (~> 1.20240722.0)
    - abseil/types/variant (~> 1.20240722.0)
    - abseil/utility/utility (~> 1.20240722.0)
    - BoringSSL-GRPC (= 0.0.37)
    - gRPC-Core/Interface (= 1.69.0)
    - gRPC-Core/Privacy (= 1.69.0)
  - gRPC-Core/Interface (1.69.0)
  - gRPC-Core/Privacy (1.69.0)
  - GTMSessionFetcher/Core (4.5.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - iris_method_channel (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - cloud_functions (from `.symlinks/plugins/cloud_functions/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - Flutter (from `Flutter`)
  - flutter_callkit_incoming (from `.symlinks/plugins/flutter_callkit_incoming/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - AppCheckCore
    - BoringSSL-GRPC
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseFunctions
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseMessagingInterop
    - FirebaseSharedSwift
    - FirebaseStorage
    - GoogleDataTransport
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - OrderedSet
    - PromisesObjC
    - RecaptchaInterop
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  cloud_functions:
    :path: ".symlinks/plugins/cloud_functions/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_callkit_incoming:
    :path: ".symlinks/plugins/flutter_callkit_incoming/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  abseil: a05cc83bf02079535e17169a73c5be5ba47f714b
  agora_rtc_engine: 55723c1e997b9f1d423d4a07dd8d9ec437a1eb89
  AgoraInfra_iOS: 3691b2b277a1712a35ae96de25af319de0d73d08
  AgoraIrisRTC_iOS: eab58c126439adf5ec99632828a558ea216860da
  AgoraRtcEngine_iOS: 97e2398a2addda9057815a2a583a658e36796ff6
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  BoringSSL-GRPC: dded2a44897e45f28f08ae87a55ee4bcd19bc508
  camera_avfoundation: adb0207d868b2d873e895371d88448399ab78d87
  cloud_firestore: d959b9b44fb0619e8c88e1b7436d3b1d36b4984e
  cloud_functions: 1e67933210f994f158a319da29e9b02cd2d0c09a
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  emoji_picker_flutter: fe2e6151c5b548e975d546e6eeb567daf0962a58
  file_picker: b159e0c068aef54932bb15dc9fd1571818edaf49
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_app_check: a3fad167381a08d4e9534da64429c9b648e51f24
  firebase_auth: 3f532201cbdc7cd6dfc3bfa89affc0c294111e20
  firebase_core: 3c2f323cae65c97a636a05a23b17730ef93df2cf
  firebase_messaging: 3b99522baf7480dfb4b7683d2b34e842d577c362
  firebase_storage: 11a326ee2116e122192f7b960db6f17ea8c18681
  FirebaseAppCheck: 9687ebd909702469bc09d2d58008697b83f4ac27
  FirebaseAppCheckInterop: 73b173e5ec45192e2d522ad43f526a82ad10b852
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: b583210c039a60ed3f1e48865e1f3da44a796595
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseFirestore: 3f1488ff7739cb3c5d10e572bc4e9fcd8e8cb4ac
  FirebaseFirestoreInternal: 97a2bb5f16951c77753c860d3519379702ab6f8a
  FirebaseFunctions: e89e0cf2091c9fb19aa58df270317c0a91963f4f
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  FirebaseMessagingInterop: 63e504b147a7206cfe64cbe2f40c2ddd009945bd
  FirebaseSharedSwift: d2475748a2d2a36242ed13baa34b2acda846c925
  FirebaseStorage: e83d1b9c8a5318d46ccfb2955f0d98095e0bf598
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_callkit_incoming: 417dd1b46541cdd5d855ad795ccbe97d1c18155e
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: df98d66e515e1ca797af436137b4459b160ad8c9
  geocoding_ios: d7460f56e80e118d57678efe5c2cdc888739ff18
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  "gRPC-C++": cc207623316fb041a7a3e774c252cf68a058b9e8
  gRPC-Core: 860978b7db482de8b4f5e10677216309b5ff6330
  GTMSessionFetcher: fc75fc972958dceedee61cb662ae1da7a83a91cf
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  iris_method_channel: 0617c689164d8154c020c50f63ca79f92b8a7b9d
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  record_darwin: 3b1a8e7d5c0cbf45ad6165b4d83a6ca643d929c3
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e

PODFILE CHECKSUM: 251cb053df7158f337c0712f2ab29f4e0fa474ce

COCOAPODS: 1.16.2
