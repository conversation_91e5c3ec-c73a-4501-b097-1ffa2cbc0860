import 'package:flutter/material.dart';
import '../../models/chat_models.dart';

class AnimatedMessageList extends StatefulWidget {
  final List<Message> messages;
  final Widget Function(Message, int) itemBuilder;
  final ScrollController? scrollController;
  final VoidCallback? onLoadMore;
  final bool isLoadingMore;
  final bool hasMoreMessages;

  const AnimatedMessageList({
    super.key,
    required this.messages,
    required this.itemBuilder,
    this.scrollController,
    this.onLoadMore,
    this.isLoadingMore = false,
    this.hasMoreMessages = true,
  });

  @override
  State<AnimatedMessageList> createState() => _AnimatedMessageListState();
}

class _AnimatedMessageListState extends State<AnimatedMessageList>
    with TickerProviderStateMixin {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<Message> _displayedMessages = [];
  late AnimationController _newMessageController;
  late Animation<double> _newMessageAnimation;
  ScrollController? _scrollController;

  @override
  void initState() {
    super.initState();
    
    // Initialize scroll controller
    _scrollController = widget.scrollController ?? ScrollController();
    
    // Initialize new message animation controller
    _newMessageController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _newMessageAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _newMessageController,
      curve: Curves.easeOutBack,
    ));

    // Initialize with current messages
    _displayedMessages = List.from(widget.messages);
    
    // Add scroll listener for load more
    _scrollController!.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(AnimatedMessageList oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update messages with animations
    _updateMessagesWithAnimation(oldWidget.messages, widget.messages);
  }

  @override
  void dispose() {
    _newMessageController.dispose();
    if (widget.scrollController == null) {
      _scrollController?.dispose();
    }
    super.dispose();
  }

  /// Handle scroll events for load more functionality
  void _onScroll() {
    if (_scrollController!.position.pixels >= 
        _scrollController!.position.maxScrollExtent - 200) {
      if (widget.hasMoreMessages && 
          !widget.isLoadingMore && 
          widget.onLoadMore != null) {
        widget.onLoadMore!();
      }
    }
  }

  /// Update messages with smooth animations
  void _updateMessagesWithAnimation(List<Message> oldList, List<Message> newList) {
    // If lists are identical, no need to animate
    if (_listsAreEqual(oldList, newList)) return;

    // Handle new messages (typically added at the beginning)
    final newMessages = _findNewMessages(oldList, newList);
    if (newMessages.isNotEmpty) {
      _animateNewMessages(newMessages, newList);
      return;
    }

    // Handle complete list replacement (e.g., initial load or refresh)
    if (_displayedMessages.isEmpty && newList.isNotEmpty) {
      _displayedMessages = List.from(newList);
      _animateInitialLoad();
      return;
    }

    // Handle other updates (status changes, etc.)
    _displayedMessages = List.from(newList);
    if (mounted) {
      setState(() {});
    }
  }

  /// Check if two message lists are equal
  bool _listsAreEqual(List<Message> list1, List<Message> list2) {
    if (list1.length != list2.length) return false;
    
    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id ||
          list1[i].status != list2[i].status ||
          list1[i].readBy?.length != list2[i].readBy?.length) {
        return false;
      }
    }
    
    return true;
  }

  /// Find new messages that were added
  List<Message> _findNewMessages(List<Message> oldList, List<Message> newList) {
    final oldIds = oldList.map((msg) => msg.id).toSet();
    return newList.where((msg) => !oldIds.contains(msg.id)).toList();
  }

  /// Animate new messages being added
  void _animateNewMessages(List<Message> newMessages, List<Message> fullList) {
    // Update displayed messages
    _displayedMessages = List.from(fullList);
    
    // Find insertion indices for new messages
    for (final newMessage in newMessages) {
      final index = _displayedMessages.indexOf(newMessage);
      if (index != -1) {
        // Insert item with animation
        _listKey.currentState?.insertItem(
          index,
          duration: const Duration(milliseconds: 300),
        );
        
        // Trigger new message animation
        _newMessageController.forward().then((_) {
          _newMessageController.reset();
        });
        
        // Auto-scroll to new message if it's at the bottom
        if (index == 0) {
          _scrollToBottom();
        }
      }
    }
  }

  /// Animate initial load of messages
  void _animateInitialLoad() {
    // Add items with staggered animation from bottom to top
    for (int i = _displayedMessages.length - 1; i >= 0; i--) {
      final delay = (_displayedMessages.length - 1 - i) * 30;
      Future.delayed(Duration(milliseconds: delay), () {
        if (mounted && _listKey.currentState != null) {
          _listKey.currentState!.insertItem(
            i, 
            duration: const Duration(milliseconds: 200),
          );
        }
      });
    }
  }

  /// Scroll to bottom of the list
  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController!.hasClients) {
        _scrollController!.animateTo(
          0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// Build animated list item
  Widget _buildAnimatedItem(BuildContext context, int index, Animation<double> animation) {
    if (index >= _displayedMessages.length) {
      return const SizedBox.shrink();
    }

    final message = _displayedMessages[index];
    final isNewMessage = index == 0 && _newMessageController.isAnimating;

    Widget child = widget.itemBuilder(message, index);

    // Apply new message animation if this is a new message
    if (isNewMessage) {
      child = AnimatedBuilder(
        animation: _newMessageAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.8 + (0.2 * _newMessageAnimation.value),
            child: Opacity(
              opacity: _newMessageAnimation.value,
              child: child,
            ),
          );
        },
        child: child,
      );
    }

    // Apply slide animation for all items
    return SlideTransition(
      position: animation.drive(
        Tween<Offset>(
          begin: const Offset(0.0, 0.3),
          end: Offset.zero,
        ).chain(CurveTween(curve: Curves.easeOutCubic)),
      ),
      child: FadeTransition(
        opacity: animation.drive(
          Tween<double>(begin: 0.0, end: 1.0)
              .chain(CurveTween(curve: Curves.easeIn)),
        ),
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Load more indicator
        if (widget.isLoadingMore)
          Container(
            padding: const EdgeInsets.all(16),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 8),
                Text(
                  'Loading more messages...',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        
        // Messages list
        Expanded(
          child: AnimatedList(
            key: _listKey,
            controller: _scrollController,
            initialItemCount: _displayedMessages.length,
            itemBuilder: _buildAnimatedItem,
            reverse: true, // Show newest messages at bottom
            physics: const AlwaysScrollableScrollPhysics(),
          ),
        ),
      ],
    );
  }
}
