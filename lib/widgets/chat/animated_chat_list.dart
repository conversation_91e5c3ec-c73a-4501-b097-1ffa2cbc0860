import 'package:flutter/material.dart';
import '../../models/chat_models.dart';
import '../../utils/app_colors.dart';

class AnimatedChatList extends StatefulWidget {
  final List<ChatRoom> chatRooms;
  final Function(ChatRoom) onChatRoomTap;
  final Widget Function(ChatRoom) itemBuilder;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const AnimatedChatList({
    super.key,
    required this.chatRooms,
    required this.onChatRoomTap,
    required this.itemBuilder,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  State<AnimatedChatList> createState() => _AnimatedChatListState();
}

class _AnimatedChatListState extends State<AnimatedChatList>
    with TickerProviderStateMixin {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<ChatRoom> _displayedChatRooms = [];
  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize refresh animation controller
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _refreshAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _refreshController, curve: Curves.easeInOut),
    );

    // Initialize with current chat rooms
    _displayedChatRooms = List.from(widget.chatRooms);
  }

  @override
  void didUpdateWidget(AnimatedChatList oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle loading state changes
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _refreshController.forward();
      } else {
        _refreshController.reverse();
      }
    }

    // Update chat rooms with animations
    _updateChatRoomsWithAnimation(oldWidget.chatRooms, widget.chatRooms);
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// Update chat rooms with smooth animations
  void _updateChatRoomsWithAnimation(
    List<ChatRoom> oldList,
    List<ChatRoom> newList,
  ) {
    // If lists are identical, no need to animate
    if (_listsAreEqual(oldList, newList)) return;

    // Handle complete list replacement (e.g., initial load)
    if (_displayedChatRooms.isEmpty && newList.isNotEmpty) {
      _displayedChatRooms = List.from(newList);
      _animateInitialLoad();
      return;
    }

    // Handle incremental updates
    _handleIncrementalUpdates(newList);
  }

  /// Check if two chat room lists are equal
  bool _listsAreEqual(List<ChatRoom> list1, List<ChatRoom> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id ||
          list1[i].lastMessage != list2[i].lastMessage ||
          list1[i].lastMessageTime != list2[i].lastMessageTime) {
        return false;
      }
    }

    return true;
  }

  /// Animate initial load of chat rooms
  void _animateInitialLoad() {
    // Add items with staggered animation
    for (int i = 0; i < _displayedChatRooms.length; i++) {
      Future.delayed(Duration(milliseconds: i * 50), () {
        if (mounted && _listKey.currentState != null) {
          _listKey.currentState!.insertItem(
            i,
            duration: const Duration(milliseconds: 300),
          );
        }
      });
    }
  }

  /// Handle incremental updates (add, remove, reorder)
  void _handleIncrementalUpdates(List<ChatRoom> newList) {
    final oldIds = _displayedChatRooms.map((room) => room.id).toSet();
    final newIds = newList.map((room) => room.id).toSet();

    // Find removed items
    final removedIds = oldIds.difference(newIds);
    for (final removedId in removedIds) {
      final index = _displayedChatRooms.indexWhere(
        (room) => room.id == removedId,
      );
      if (index != -1) {
        final removedRoom = _displayedChatRooms.removeAt(index);
        _listKey.currentState?.removeItem(
          index,
          (context, animation) => _buildRemovedItem(removedRoom, animation),
          duration: const Duration(milliseconds: 300),
        );
      }
    }

    // Find added items
    final addedIds = newIds.difference(oldIds);
    for (final addedId in addedIds) {
      final newRoom = newList.firstWhere((room) => room.id == addedId);
      final insertIndex = newList.indexOf(newRoom);

      if (insertIndex <= _displayedChatRooms.length) {
        _displayedChatRooms.insert(insertIndex, newRoom);
        _listKey.currentState?.insertItem(
          insertIndex,
          duration: const Duration(milliseconds: 300),
        );
      }
    }

    // Update existing items (for message changes, etc.)
    for (int i = 0; i < newList.length && i < _displayedChatRooms.length; i++) {
      if (_displayedChatRooms[i].id == newList[i].id) {
        _displayedChatRooms[i] = newList[i];
      }
    }

    // Trigger rebuild for updated items
    if (mounted) {
      setState(() {});
    }
  }

  /// Build removed item animation
  Widget _buildRemovedItem(ChatRoom chatRoom, Animation<double> animation) {
    return SlideTransition(
      position: animation.drive(
        Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(-1.0, 0.0),
        ).chain(CurveTween(curve: Curves.easeInOut)),
      ),
      child: FadeTransition(
        opacity: animation,
        child: widget.itemBuilder(chatRoom),
      ),
    );
  }

  /// Build animated list item
  Widget _buildAnimatedItem(
    BuildContext context,
    int index,
    Animation<double> animation,
  ) {
    if (index >= _displayedChatRooms.length) {
      return const SizedBox.shrink();
    }

    final chatRoom = _displayedChatRooms[index];

    return SlideTransition(
      position: animation.drive(
        Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).chain(CurveTween(curve: Curves.easeOutCubic)),
      ),
      child: FadeTransition(
        opacity: animation.drive(
          Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).chain(CurveTween(curve: Curves.easeIn)),
        ),
        child: GestureDetector(
          onTap: () => widget.onChatRoomTap(chatRoom),
          child: widget.itemBuilder(chatRoom),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        if (widget.onRefresh != null) {
          widget.onRefresh!();
          // Wait for refresh animation
          await Future.delayed(const Duration(milliseconds: 500));
        }
      },
      child: Stack(
        children: [
          // Main animated list
          AnimatedList(
            key: _listKey,
            initialItemCount: _displayedChatRooms.length,
            itemBuilder: _buildAnimatedItem,
            physics: const AlwaysScrollableScrollPhysics(),
          ),

          // Loading overlay
          if (widget.isLoading)
            AnimatedBuilder(
              animation: _refreshAnimation,
              builder: (context, child) {
                return Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Transform.translate(
                    offset: Offset(0, -50 + (50 * _refreshAnimation.value)),
                    child: Container(
                      height: 4,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.appColor.withOpacity(0.0),
                            AppColors.appColor.withOpacity(
                              _refreshAnimation.value,
                            ),
                            AppColors.appColor.withOpacity(0.0),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),

          // Empty state
          if (_displayedChatRooms.isEmpty && !widget.isLoading)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No conversations yet',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start a conversation to see it here',
                    style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
