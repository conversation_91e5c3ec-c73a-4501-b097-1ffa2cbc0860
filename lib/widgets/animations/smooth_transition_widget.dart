import 'package:flutter/material.dart';

/// A widget that provides smooth transitions for content changes
class SmoothTransitionWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final bool fadeTransition;
  final bool slideTransition;
  final bool scaleTransition;
  final Offset? slideOffset;

  const SmoothTransitionWidget({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.fadeTransition = true,
    this.slideTransition = false,
    this.scaleTransition = false,
    this.slideOffset,
  });

  @override
  State<SmoothTransitionWidget> createState() => _SmoothTransitionWidgetState();
}

class _SmoothTransitionWidgetState extends State<SmoothTransitionWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  Widget? _currentChild;
  Widget? _previousChild;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _slideAnimation = Tween<Offset>(
      begin: widget.slideOffset ?? const Offset(0.0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _currentChild = widget.child;
    _controller.forward();
  }

  @override
  void didUpdateWidget(SmoothTransitionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.child != oldWidget.child) {
      _animateToNewChild(widget.child);
    }
    
    if (widget.duration != oldWidget.duration) {
      _controller.duration = widget.duration;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Animate to new child widget
  void _animateToNewChild(Widget newChild) {
    _previousChild = _currentChild;
    _currentChild = newChild;
    
    _controller.reset();
    _controller.forward();
  }

  /// Build the animated widget
  Widget _buildAnimatedChild(Widget child) {
    Widget animatedChild = child;

    // Apply scale transition
    if (widget.scaleTransition) {
      animatedChild = ScaleTransition(
        scale: _scaleAnimation,
        child: animatedChild,
      );
    }

    // Apply slide transition
    if (widget.slideTransition) {
      animatedChild = SlideTransition(
        position: _slideAnimation,
        child: animatedChild,
      );
    }

    // Apply fade transition
    if (widget.fadeTransition) {
      animatedChild = FadeTransition(
        opacity: _fadeAnimation,
        child: animatedChild,
      );
    }

    return animatedChild;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return _buildAnimatedChild(_currentChild!);
      },
    );
  }
}

/// A widget that provides smooth loading state transitions
class SmoothLoadingTransition extends StatefulWidget {
  final bool isLoading;
  final Widget child;
  final Widget? loadingWidget;
  final Duration duration;

  const SmoothLoadingTransition({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingWidget,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  State<SmoothLoadingTransition> createState() => _SmoothLoadingTransitionState();
}

class _SmoothLoadingTransitionState extends State<SmoothLoadingTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (!widget.isLoading) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(SmoothLoadingTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _controller.reverse();
      } else {
        _controller.forward();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Stack(
          children: [
            // Loading widget
            if (widget.isLoading)
              FadeTransition(
                opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                  CurvedAnimation(
                    parent: _controller,
                    curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
                  ),
                ),
                child: widget.loadingWidget ?? 
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
              ),
            
            // Content widget
            FadeTransition(
              opacity: _animation,
              child: Transform.scale(
                scale: 0.95 + (0.05 * _animation.value),
                child: widget.child,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// A widget that provides smooth list item animations
class SmoothListItemTransition extends StatefulWidget {
  final Widget child;
  final int index;
  final Duration delay;
  final Duration duration;

  const SmoothListItemTransition({
    super.key,
    required this.child,
    required this.index,
    this.delay = const Duration(milliseconds: 50),
    this.duration = const Duration(milliseconds: 400),
  });

  @override
  State<SmoothListItemTransition> createState() => _SmoothListItemTransitionState();
}

class _SmoothListItemTransitionState extends State<SmoothListItemTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    // Start animation with delay based on index
    Future.delayed(
      Duration(milliseconds: widget.index * widget.delay.inMilliseconds),
      () {
        if (mounted) {
          _controller.forward();
        }
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: widget.child,
          ),
        );
      },
    );
  }
}
