import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/call_provider.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/services/call_service.dart';
import 'package:tolk/services/token_service.dart';
import 'package:tolk/screens/call/audio_call_screen.dart';
import 'package:tolk/screens/call/video_call_screen.dart';

class IncomingCallListener extends StatefulWidget {
  final Widget child;

  const IncomingCallListener({super.key, required this.child});

  @override
  State<IncomingCallListener> createState() => _IncomingCallListenerState();

  // Static method to trigger overlay from notification service
  static void triggerCallOverlay(Map<String, dynamic> notificationData) {
    _IncomingCallListenerState.triggerCallOverlay(notificationData);
  }
}

class _IncomingCallListenerState extends State<IncomingCallListener> {
  final CallService _callService = CallService();
  String? _currentCallId; // Track current call to prevent duplicate navigation

  // Static reference to allow external triggering
  static _IncomingCallListenerState? _instance;

  @override
  void initState() {
    super.initState();
    _instance = this; // Set static reference
    print(
      '📞 [CALL_LISTENER] ========== INCOMING CALL LISTENER INIT ==========',
    );
    print('📞 [CALL_LISTENER] Widget initialized');
  }

  @override
  void dispose() {
    _instance = null; // Clear static reference
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final currentUser = userProvider.currentUser;
    final callProvider = Provider.of<CallProvider>(context, listen: false);

    // print('📞 [CALL_LISTENER] ========== BUILD CALLED ==========');
    // print('📞 [CALL_LISTENER] UserProvider loading: ${userProvider.isLoading}');
    // print('📞 [CALL_LISTENER] Current user: ${currentUser?.uid}');
    // print('📞 [CALL_LISTENER] User name: ${currentUser?.name}');

    if (currentUser == null) {
      // print('📞 [CALL_LISTENER] ❌ No current user, skipping call listener');
      // If user logs out, ensure any existing call in provider is cleared.
      if (callProvider.incomingCall != null) {
        print(
          '📞 [CALL_LISTENER] User logged out, clearing call from provider.',
        );
        WidgetsBinding.instance.addPostFrameCallback((_) {
          callProvider.clearIncomingCall();
        });
      }
      return widget.child;
    }

    // print('📞 [CALL_LISTENER] ✅ Setting up call listener for user: ${currentUser.uid}');

    return StreamBuilder<CallData?>(
      stream: _callService.listenForIncomingCalls(currentUser.uid),
      builder: (context, snapshot) {
        print('📞 [CALL_LISTENER] ========== STREAM UPDATE ==========');
        print(
          '📞 [CALL_LISTENER] Connection state: ${snapshot.connectionState}',
        );
        print('📞 [CALL_LISTENER] Has data: ${snapshot.hasData}');
        print('📞 [CALL_LISTENER] Has error: ${snapshot.hasError}');
        if (snapshot.hasError) {
          print('📞 [CALL_LISTENER] ❌ Stream error: ${snapshot.error}');
        }
        print('📞 [CALL_LISTENER] Data: ${snapshot.data?.callId}');
        print('📞 [CALL_LISTENER] Data status: ${snapshot.data?.status}');

        if (snapshot.connectionState == ConnectionState.waiting &&
            callProvider.incomingCall == null) {
          // Still waiting for initial data and no call is active in provider
          return widget.child;
        }

        if (snapshot.hasError) {
          print(
            '📞 [CALL_LISTENER] ❌ Stream error: ${snapshot.error}, clearing call from provider.',
          );
          WidgetsBinding.instance.addPostFrameCallback((_) {
            callProvider.clearIncomingCall();
          });
          return widget.child;
        }

        final callData = snapshot.data;

        if (callData != null && callData.status == 'calling') {
          print(
            '📞 [CALL_LISTENER] ✅ Incoming call detected: ${callData.callId} from ${callData.callerName}',
          );
          print('📞 [CALL_LISTENER] Call status: ${callData.status}');
          print('📞 [CALL_LISTENER] Call type: ${callData.type}');
          print('📞 [CALL_LISTENER] Current call ID: $_currentCallId');
          print(
            '📞 [CALL_LISTENER] Call screen open: ${callProvider.isCallScreenOpen}',
          );

          // Update provider only if it's a new call or status changed for the current call
          if (callProvider.incomingCall?.callId != callData.callId ||
              callProvider.incomingCall?.status != callData.status) {
            print(
              '📞 [CALL_LISTENER] Updating CallProvider with new call data: ${callData.callId}',
            );
            WidgetsBinding.instance.addPostFrameCallback((_) {
              callProvider.setIncomingCall(callData);

              // Automatically navigate to call screen if this is a new call
              if (_currentCallId != callData.callId &&
                  !callProvider.isCallScreenOpen) {
                print(
                  '📞 [CALL_LISTENER] Triggering auto-overlay for new call: ${callData.callId}',
                );
                _currentCallId = callData.callId;
                _navigateToIncomingCallScreen(callData, currentUser);
              } else {
                print(
                  '📞 [CALL_LISTENER] Skipping overlay - Current call ID: $_currentCallId, New call ID: ${callData.callId}, Screen open: ${callProvider.isCallScreenOpen}',
                );
              }
            });
          } else {
            print('📞 [CALL_LISTENER] Same call data, no update needed');
          }
        } else {
          // No active 'calling' call, or call status is no longer 'calling'
          if (callProvider.incomingCall != null) {
            // If there was a call in the provider, clear it
            // This handles cases where the call is ended/declined/timed out on the other end
            // or if the stream returns null (e.g. call document deleted)
            print(
              '📞 [CALL_LISTENER] No active call or status not "calling", clearing call from provider. Current provider call: ${callProvider.incomingCall?.callId}, status: ${callProvider.incomingCall?.status}. Stream data: ${callData?.callId}, status: ${callData?.status}',
            );
            WidgetsBinding.instance.addPostFrameCallback((_) {
              callProvider.clearIncomingCall();
              // Clear current call ID when call ends
              _currentCallId = null;
            });
          }
        }

        return widget.child;
      },
    );
  }

  // Show incoming call screen as full-screen modal overlay
  void _navigateToIncomingCallScreen(CallData callData, currentUser) {
    if (!mounted) return;

    // Additional safety check - ensure we have a valid context and user
    if (currentUser == null) {
      print('📞 [CALL_LISTENER] No current user, cannot show call screen');
      return;
    }

    // Check if call screen is already open to prevent duplicate overlays
    final callProvider = Provider.of<CallProvider>(context, listen: false);
    if (callProvider.isCallScreenOpen) {
      print('📞 [CALL_LISTENER] Call screen already open, skipping overlay');
      return;
    }

    print(
      '📞 [CALL_LISTENER] Auto-showing call screen overlay for call: ${callData.callId}',
    );

    try {
      // Mark call screen as opening to prevent duplicate navigation
      callProvider.setCallScreenOpen(true);

      // Generate Agora UID for the receiver (current user)
      final int uid =
          callData.receiverAgoraUid ??
          TokenService.generateUid(currentUser.uid);

      // Create the appropriate call screen widget
      Widget callScreen;
      if (callData.type == CallType.audio) {
        callScreen = AudioCallScreen(
          channelName: callData.channelName,
          uid: uid,
          callerName: callData.callerName,
          callerAvatar: callData.callerAvatar,
          isIncoming: true,
          callId: callData.callId,
          launchedFromNotification: false,
        );
      } else {
        callScreen = VideoCallScreen(
          channelName: callData.channelName,
          uid: uid,
          callerName: callData.callerName,
          callerAvatar: callData.callerAvatar,
          isIncoming: true,
          callId: callData.callId,
          launchedFromNotification: false,
        );
      }

      // Show as full-screen modal overlay
      showDialog(
        context: context,
        barrierDismissible: false, // Prevent dismissing by tapping outside
        useSafeArea: false, // Use full screen
        builder: (BuildContext context) {
          return PopScope(
            canPop: false, // Prevent back button dismissal
            child: callScreen,
          );
        },
      ).then((_) {
        // Reset call screen status when dialog is dismissed
        if (mounted) {
          final callProvider = Provider.of<CallProvider>(
            context,
            listen: false,
          );
          callProvider.setCallScreenOpen(false);
          _currentCallId = null;
        }
      });

      print('📞 [CALL_LISTENER] ✅ Call screen overlay shown successfully');
    } catch (e) {
      print('📞 [CALL_LISTENER] ❌ Error showing call screen overlay: $e');
      // Reset call screen open status on error
      final callProvider = Provider.of<CallProvider>(context, listen: false);
      callProvider.setCallScreenOpen(false);
    }
  }

  // Static method to trigger overlay from notification service
  static void triggerCallOverlay(Map<String, dynamic> notificationData) {
    print(
      '📞 [CALL_LISTENER] ========== TRIGGER OVERLAY FROM NOTIFICATION ==========',
    );
    print('📞 [CALL_LISTENER] Notification data: $notificationData');

    if (_instance == null || !_instance!.mounted) {
      print(
        '📞 [CALL_LISTENER] ❌ No active instance or not mounted, cannot show overlay',
      );
      return;
    }

    try {
      // Extract call data from notification
      final callId = notificationData['callId'] ?? '';
      final callerName = notificationData['callerName'] ?? 'Unknown';
      final callerId = notificationData['callerId'] ?? '';
      final callType = notificationData['callType'] ?? 'audio';
      final channelName = notificationData['channelName'] ?? '';
      final callerAvatar = notificationData['callerAvatar'];
      final callerAgoraUid =
          int.tryParse(notificationData['callerAgoraUid'] ?? '0') ?? 0;
      final receiverAgoraUid =
          int.tryParse(notificationData['receiverAgoraUid'] ?? '0') ?? 0;

      print('📞 [CALL_LISTENER] Extracted call data:');
      print('📞 [CALL_LISTENER] - Call ID: $callId');
      print('📞 [CALL_LISTENER] - Caller: $callerName');
      print('📞 [CALL_LISTENER] - Type: $callType');

      // Get current user from provider first
      final userProvider = Provider.of<UserProvider>(
        _instance!.context,
        listen: false,
      );
      final currentUser = userProvider.currentUser;

      if (currentUser == null) {
        print('📞 [CALL_LISTENER] ❌ No current user, cannot show overlay');
        return;
      }

      // Create CallData object
      final callData = CallData(
        callId: callId,
        callerId: callerId,
        callerName: callerName,
        callerAvatar: callerAvatar,
        receiverId: currentUser.uid, // Use current user's ID
        type: callType == 'video' ? CallType.video : CallType.audio,
        channelName: channelName,
        status: 'calling',
        timestamp: DateTime.now(),
        callerAgoraUid: callerAgoraUid,
        receiverAgoraUid: receiverAgoraUid,
      );

      print(
        '📞 [CALL_LISTENER] Triggering overlay for user: ${currentUser.uid}',
      );
      _instance!._navigateToIncomingCallScreen(callData, currentUser);
    } catch (e) {
      print('📞 [CALL_LISTENER] ❌ Error triggering overlay: $e');
    }
  }
}
// Removed IncomingCallScreen and _showIncomingCallDialog as it's no longer used here.
// The UI for incoming calls will be handled by ChatListScreen via CallProvider.