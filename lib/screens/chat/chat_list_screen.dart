import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/providers/call_provider.dart';
import 'package:tolk/services/call_service.dart';
import 'package:tolk/screens/call/audio_call_screen.dart';
import 'package:tolk/screens/call/video_call_screen.dart';
import 'package:tolk/services/token_service.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/providers/contact_provider.dart';
import 'package:tolk/providers/chat_list_provider.dart';
import 'package:tolk/screens/chat/chat_screen.dart';
import 'package:tolk/screens/chat/user_search_screen.dart';
import 'package:tolk/screens/chat/group_create_screen.dart';
import 'package:tolk/screens/profile/profile_screen.dart';
import 'package:tolk/screens/settings/settings_screen.dart'; // Import SettingsScreen
import 'package:tolk/services/chat_service.dart';
import 'package:tolk/services/notification_service.dart';
import 'package:tolk/services/permission_service.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/utils/app_strings.dart';
import 'package:tolk/utils/utilities.dart';
import 'package:tolk/widgets/permission_check_widget.dart';
import 'package:tolk/services/contact_service.dart';
import 'package:tolk/widgets/chat/translated_chat_list_message_widget.dart';

class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  final ChatService _chatService = ChatService();
  final CallService _callService = CallService(); // Add CallService instance
  late TabController _tabController;
  CallData? _currentCallData; // To hold current call data for the UI

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addObserver(this);
    _updateUserOnlineStatus(true);
    _initializeNotifications();
    // Don't load contacts here - wait for PermissionCheckWidget to complete
  }

  Future<void> _initializeNotifications() async {
    try {
      print('🔔 [CHAT_LIST] Ensuring notifications are initialized...');
      await NotificationService().initialize();
    } catch (e) {
      print('🔔 [CHAT_LIST] Error initializing notifications: $e');
    }
  }

  /// Load contacts in background for instant search access (only if permission granted)
  void _loadContactsInBackground() {
    // Use post frame callback to ensure the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final contactProvider = Provider.of<ContactProvider>(
        context,
        listen: false,
      );

      // Start background loading (will check permission first)
      contactProvider.loadContactsInBackground().catchError((error) {
        // Log error but don't show to user since this is background loading
        print('🔔 [CHAT_LIST] Background contact loading error: $error');
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _updateUserOnlineStatus(false);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _updateUserOnlineStatus(true);
    } else {
      _updateUserOnlineStatus(false);
    }
  }

  void _updateUserOnlineStatus(bool isOnline) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.updateOnlineStatus(isOnline);
  }

  void _onPermissionsGranted() {
    setState(() {
      // Permissions have been granted, can proceed with app functionality
    });

    // Now that PermissionCheckWidget is closed, start contact loading
    _loadContactsInBackground();
  }

  Future<void> _checkContactPermissionAndOpenSearch() async {
    final contactService = ContactService();
    final contactProvider = Provider.of<ContactProvider>(
      context,
      listen: false,
    );

    // Check if contacts permission is granted
    final hasPermission = await contactService.hasContactsPermission();

    if (!hasPermission) {
      // Show permission dialog
      final shouldRequest = await _showContactPermissionDialog();

      if (shouldRequest) {
        // Request permission
        final granted = await contactService.requestContactsPermission();

        if (granted) {
          // Update permission status in provider and load contacts if needed
          await contactProvider.updateContactsPermission();

          // Permission granted, open search
          if (mounted) {
            pushScreen(context, const UserSearchScreen());
          }
        } else {
          // Permission denied, show info message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Contacts permission is needed to search your contacts',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }
    } else {
      // Permission already granted, open search directly
      if (mounted) {
        pushScreen(context, const UserSearchScreen());
      }
    }
  }

  Future<bool> _showContactPermissionDialog() async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                backgroundColor: AppColors.splashColor,
                title: const Text(
                  'Contacts Permission',
                  style: TextStyle(color: Colors.white),
                ),
                content: const Text(
                  'To search your contacts and find friends on Tolk, we need access to your contacts. This helps you connect with people you know.',
                  style: TextStyle(color: Colors.white70),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text(
                      'Not Now',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.appColor,
                    ),
                    child: const Text(
                      'Allow Access',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final callProvider = Provider.of<CallProvider>(context);
    _currentCallData = callProvider.incomingCall;

    // Determine if the call screen is already open to prevent showing the inline notification
    final bool isCallScreenActuallyOpen = callProvider.isCallScreenOpen;

    final chatContent = Scaffold(
      backgroundColor: AppColors.splashColor,
      body: SafeArea(
        child: Column(
          children: [
            // Incoming Call Notification
            if (_currentCallData != null &&
                _currentCallData!.status == 'calling' &&
                !isCallScreenActuallyOpen)
              _buildIncomingCallNotification(_currentCallData!),
            // Custom Header with Profile
            _buildCustomHeader(),
            const SizedBox(height: 8),
            // Tab Bar
            _buildTabBar(),
            const SizedBox(height: 16),
            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Chats Tab
                  _buildChatsTab(),
                  // Groups Tab
                  _buildGroupsTab(),
                  // Profile Tab
                  _buildProfileTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Hide FAB on Profile tab
          if (_tabController.index == 2) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton(
            onPressed: () {
              if (_tabController.index == 0) {
                // Chats tab - open user search
                pushScreen(context, const UserSearchScreen());
              } else if (_tabController.index == 1) {
                // Groups tab - open group creation screen
                pushScreen(context, const GroupCreateScreen());
              }
            },
            backgroundColor: AppColors.appColor,
            child: Icon(
              _tabController.index == 0 ? Icons.chat : Icons.group_add,
              color: Colors.white,
            ),
          );
        },
      ),
    );

    // Wrap the chat content with permission check
    return PermissionCheckWidget(
      onPermissionsGranted: _onPermissionsGranted,
      child: chatContent,
    );
  }

  Widget _buildCustomHeader() {
    final currentUser = Provider.of<UserProvider>(context).currentUser;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Profile Image
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey,
            backgroundImage:
                currentUser?.profilePicture != null
                    ? CachedNetworkImageProvider(currentUser!.profilePicture!)
                    : null,
            child:
                currentUser?.profilePicture == null
                    ? const Icon(Icons.person, color: Colors.white, size: 24)
                    : null,
          ),
          const SizedBox(width: 12),
          // User Name
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  currentUser?.name ?? 'User',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Welcome back!',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ),
          // Search Icon
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: _checkContactPermissionAndOpenSearch,
          ),
          // Settings Icon
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.appColor,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
        tabs: const [
          Tab(icon: Icon(Icons.chat, size: 20), text: 'Chats'),
          Tab(icon: Icon(Icons.group, size: 20), text: 'Groups'),
          Tab(icon: Icon(Icons.person, size: 20), text: 'Profile'),
        ],
      ),
    );
  }

  Widget _buildChatsTab() {
    return StreamBuilder<List<ChatRoom>>(
      stream: _chatService.getChatRooms(),
      builder: (context, snapshot) {
        // Show loading only on initial load, not on subsequent updates
        if (snapshot.connectionState == ConnectionState.waiting &&
            !snapshot.hasData) {
          return const Center(
            child: CircularProgressIndicator(color: AppColors.appColor),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error: ${snapshot.error}',
              style: const TextStyle(color: Colors.white),
            ),
          );
        }

        final allChatRooms = snapshot.data ?? [];
        final individualChatRooms =
            allChatRooms.where((room) => !room.isGroupChat).toList();

        if (individualChatRooms.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.chat_bubble_outline,
                  size: 80,
                  color: Colors.white54,
                ),
                const SizedBox(height: 16),
                const Text(
                  AppStrings.noChats,
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  AppStrings.startChatting,
                  style: TextStyle(color: Colors.white70),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _checkContactPermissionAndOpenSearch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: const Text(AppStrings.newChat),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: individualChatRooms.length,
          itemBuilder: (context, index) {
            final chatRoom = individualChatRooms[index];
            return _buildChatRoomTile(chatRoom);
          },
        );
      },
    );
  }

  Widget _buildGroupsTab() {
    final currentUser = Provider.of<UserProvider>(context).currentUser;
    if (currentUser == null) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.appColor),
      );
    }
    return StreamBuilder<List<ChatRoom>>(
      stream: _chatService.getChatRooms(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting &&
            !snapshot.hasData) {
          return const Center(
            child: CircularProgressIndicator(color: AppColors.appColor),
          );
        }
        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error: ${snapshot.error}',
              style: const TextStyle(color: Colors.white),
            ),
          );
        }
        final groupChats =
            (snapshot.data ?? []).where((room) => room.isGroupChat).toList();
        if (groupChats.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.group, size: 80, color: Colors.white54),
                const SizedBox(height: 16),
                const Text(
                  'No Groups',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'You are not a member of any group chats yet.',
                  style: TextStyle(color: Colors.white70),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  icon: const Icon(Icons.group_add),
                  label: const Text('Create Group'),
                  onPressed: () {
                    pushScreen(context, const GroupCreateScreen());
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        return ListView.builder(
          itemCount: groupChats.length,
          itemBuilder: (context, index) {
            final groupRoom = groupChats[index];
            return _buildGroupChatRoomTile(groupRoom, currentUser.uid);
          },
        );
      },
    );
  }

  Widget _buildGroupChatRoomTile(ChatRoom groupRoom, String currentUserId) {
    final unreadCount = groupRoom.unreadCount[currentUserId] ?? 0;
    final groupName = groupRoom.groupName ?? 'Group';
    final groupImage = groupRoom.groupImage;
    return ListTile(
      onTap: () {
        pushScreen(
          context,
          ChatScreen(chatRoomId: groupRoom.id, otherUser: null),
        );
      },
      leading: CircleAvatar(
        radius: 24,
        backgroundColor: Colors.grey,
        backgroundImage:
            groupImage != null ? CachedNetworkImageProvider(groupImage) : null,
        child:
            groupImage == null
                ? const Icon(Icons.group, color: Colors.white)
                : null,
      ),
      title: Text(
        groupName,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      subtitle: TranslatedChatListMessageWidget(
        lastMessage: groupRoom.lastMessage,
        chatRoomId: groupRoom.id,
        unreadCount: unreadCount,
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (groupRoom.lastMessageTime != null)
            Text(
              _formatTime(groupRoom.lastMessageTime!),
              style: TextStyle(
                fontSize: 12,
                color: unreadCount > 0 ? Colors.white : Colors.white70,
              ),
            ),
          const SizedBox(height: 4),
          if (unreadCount > 0)
            Container(
              padding: const EdgeInsets.all(6),
              decoration: const BoxDecoration(
                color: AppColors.appColor,
                shape: BoxShape.circle,
              ),
              child: Text(
                unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileTab() {
    final currentUser = Provider.of<UserProvider>(context).currentUser;
    if (currentUser == null) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.appColor),
      );
    }

    return ProfileScreen(user: currentUser);
  }

  Widget _buildChatRoomTile(ChatRoom chatRoom) {
    final currentUser = Provider.of<UserProvider>(context).currentUser;
    if (currentUser == null) return const SizedBox.shrink();

    final otherUserId = chatRoom.getOtherParticipant(currentUser.uid);
    final unreadCount = chatRoom.unreadCount[currentUser.uid] ?? 0;

    return FutureBuilder<UserModel?>(
      future: _chatService.getUserById(otherUserId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.grey,
              child: Icon(Icons.person, color: Colors.white),
            ),
            title: Text('Loading...', style: TextStyle(color: Colors.white)),
          );
        }

        final otherUser = snapshot.data;

        // Get saved contact name if available, otherwise use registered name
        final contactProvider = Provider.of<ContactProvider>(
          context,
          listen: false,
        );
        String name = otherUser?.name ?? otherUser?.phoneNumber ?? 'Unknown';

        if (otherUser != null) {
          final savedContact = contactProvider.savedContacts.firstWhere(
            (contact) => contact.phoneNumbers.contains(otherUser.phoneNumber),
            orElse:
                () => ContactModel(id: '', displayName: '', phoneNumbers: []),
          );

          // Use saved contact name if available
          if (savedContact.displayName.isNotEmpty) {
            name = savedContact.displayName;
          }
        }

        final profilePicture = otherUser?.profilePicture;
        final isOnline = otherUser?.isOnline ?? false;

        return ListTile(
          onTap: () async {
            // Store context before async operation
            final currentContext = context;

            // Navigate to chat screen
            if (mounted && currentContext.mounted) {
              pushScreen(
                currentContext,
                ChatScreen(chatRoomId: chatRoom.id, otherUser: otherUser),
              );
            }
          },
          leading: Stack(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: Colors.grey,
                backgroundImage:
                    profilePicture != null
                        ? CachedNetworkImageProvider(profilePicture)
                        : null,
                child:
                    profilePicture == null
                        ? const Icon(Icons.person, color: Colors.white)
                        : null,
              ),
              if (isOnline)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.splashColor,
                        width: 2,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          title: Text(
            name,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          subtitle: TranslatedChatListMessageWidget(
            lastMessage: chatRoom.lastMessage,
            chatRoomId: chatRoom.id,
            unreadCount: unreadCount,
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (chatRoom.lastMessageTime != null)
                Text(
                  _formatTime(chatRoom.lastMessageTime!),
                  style: TextStyle(
                    fontSize: 12,
                    color: unreadCount > 0 ? Colors.white : Colors.white70,
                  ),
                ),
              const SizedBox(height: 4),
              if (unreadCount > 0)
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                    color: AppColors.appColor,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    unreadCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      return DateFormat.jm().format(dateTime); // 5:08 PM
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat.MMMd().format(dateTime); // Jan 5
    }
  }

  Widget _buildIncomingCallNotification(CallData callData) {
    final callProvider = Provider.of<CallProvider>(context, listen: false);
    final currentUser =
        Provider.of<UserProvider>(context, listen: false).currentUser;

    if (currentUser == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: Colors.green.withOpacity(0.9),
      child: Row(
        children: [
          Icon(
            callData.type == CallType.audio ? Icons.call : Icons.videocam,
            color: Colors.white,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              '${callData.callerName} is calling...',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          TextButton(
            onPressed: () async {
              print('📞 [CHAT_LIST] Call rejected: ${callData.callId}');
              try {
                await _callService.declineCall(callData.callId);
                // Notification cancellation is handled within declineCall
              } catch (e) {
                print('📞 [CHAT_LIST] Error declining call: $e');
                // Optionally show a snackbar or toast for error
              }
              callProvider.clearIncomingCall(); // Clear call from provider
            },
            child: const Text(
              'Reject',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: () async {
              print('📞 [CHAT_LIST] Call accepted: ${callData.callId}');
              callProvider.setCallScreenOpen(
                true,
              ); // Indicate call screen is being opened
              callProvider
                  .clearIncomingCall(); // Clear the incoming call from provider first

              try {
                await _callService.answerCall(callData.callId);
                // Notification cancellation is handled within answerCall

                // final uid = TokenService.generateUid(currentUser.uid); // No longer needed here, handleIncomingCallNavigation will use callData.receiverAgoraUid

                if (mounted) {
                  // Use CallService.handleIncomingCallNavigation
                  _callService.handleIncomingCallNavigation(
                    context: context,
                    callData: callData,
                    currentUser: currentUser, // This is the receiver
                    launchedFromNotification:
                        false, // Explicitly false for in-app acceptance
                  );
                  // The .then((_) => callProvider.setCallScreenOpen(false)) should ideally be handled
                  // by the call screen itself upon its disposal or when the call ends.
                  // The CallProvider.setCallScreenOpen(true) was already called.
                  // The new logic in Audio/VideoCallScreen's dispose/ _handleCallScreenDismiss handles setting it to false.
                }
              } catch (e) {
                print('📞 [CHAT_LIST] Error accepting call: $e');
                callProvider.setCallScreenOpen(false); // Reset on error
                // Optionally show a snackbar or toast for error
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error accepting call: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.greenAccent,
            ),
            child: const Text(
              'Accept',
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
