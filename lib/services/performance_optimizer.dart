import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import '../models/chat_models.dart';
import '../services/local_storage_service.dart';
import '../services/cached_translation_service.dart';

class PerformanceOptimizer {
  static PerformanceOptimizer? _instance;
  static PerformanceOptimizer get instance => _instance ??= PerformanceOptimizer._();

  PerformanceOptimizer._();

  final LocalStorageService _localStorage = LocalStorageService.instance;

  // Memory management
  final LRUCache<String, List<Message>> _messageCache = LRUCache<String, List<Message>>(maxSize: 20);
  final LRUCache<String, ChatRoom> _chatRoomCache = LRUCache<String, ChatRoom>(maxSize: 100);
  final Set<String> _activeChatRooms = <String>{};
  
  // Performance monitoring
  final Map<String, DateTime> _operationTimestamps = {};
  final Map<String, int> _operationCounts = {};
  
  // Cleanup timers
  Timer? _memoryCleanupTimer;
  Timer? _cacheOptimizationTimer;

  /// Initialize performance optimizer
  void initialize() {
    debugPrint('🚀 [PERFORMANCE] Initializing performance optimizer...');
    
    // Start periodic memory cleanup
    _startMemoryCleanup();
    
    // Start cache optimization
    _startCacheOptimization();
    
    debugPrint('🚀 [PERFORMANCE] Performance optimizer initialized');
  }

  /// Start periodic memory cleanup
  void _startMemoryCleanup() {
    _memoryCleanupTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _performMemoryCleanup(),
    );
  }

  /// Start cache optimization
  void _startCacheOptimization() {
    _cacheOptimizationTimer = Timer.periodic(
      const Duration(minutes: 10),
      (_) => _optimizeCaches(),
    );
  }

  /// Perform memory cleanup
  void _performMemoryCleanup() {
    try {
      debugPrint('🚀 [PERFORMANCE] Performing memory cleanup...');
      
      // Clean up inactive chat room caches
      final inactiveChatRooms = _messageCache.keys.where(
        (chatRoomId) => !_activeChatRooms.contains(chatRoomId),
      ).toList();
      
      for (final chatRoomId in inactiveChatRooms) {
        _messageCache.remove(chatRoomId);
      }
      
      // Clean up old operation timestamps
      final cutoffTime = DateTime.now().subtract(const Duration(hours: 1));
      _operationTimestamps.removeWhere((key, timestamp) => timestamp.isBefore(cutoffTime));
      
      // Reset operation counts
      _operationCounts.clear();
      
      debugPrint('🚀 [PERFORMANCE] Memory cleanup completed. Removed ${inactiveChatRooms.length} inactive caches');
    } catch (e) {
      debugPrint('❌ [PERFORMANCE] Error during memory cleanup: $e');
    }
  }

  /// Optimize caches for better performance
  void _optimizeCaches() {
    try {
      debugPrint('🚀 [PERFORMANCE] Optimizing caches...');
      
      // Optimize translation cache
      CachedTranslationService.optimizeCache();
      
      // Optimize local storage cache
      _localStorage.optimizeCache();
      
      debugPrint('🚀 [PERFORMANCE] Cache optimization completed');
    } catch (e) {
      debugPrint('❌ [PERFORMANCE] Error during cache optimization: $e');
    }
  }

  /// Get messages with performance optimization
  List<Message> getOptimizedMessages(String chatRoomId, {int limit = 15}) {
    _trackOperation('getMessages');
    
    // Check memory cache first
    final cacheKey = '${chatRoomId}_$limit';
    if (_messageCache.containsKey(cacheKey)) {
      debugPrint('🚀 [PERFORMANCE] Cache hit for messages: $chatRoomId');
      return _messageCache.get(cacheKey)!;
    }
    
    // Load from local storage
    final messages = _localStorage.getLatestMessages(chatRoomId, limit: limit);
    
    // Cache the result
    _messageCache.put(cacheKey, messages);
    
    debugPrint('🚀 [PERFORMANCE] Loaded and cached ${messages.length} messages for: $chatRoomId');
    return messages;
  }

  /// Get chat room with performance optimization
  ChatRoom? getOptimizedChatRoom(String chatRoomId) {
    _trackOperation('getChatRoom');
    
    // Check memory cache first
    if (_chatRoomCache.containsKey(chatRoomId)) {
      debugPrint('🚀 [PERFORMANCE] Cache hit for chat room: $chatRoomId');
      return _chatRoomCache.get(chatRoomId);
    }
    
    // Load from local storage
    final chatRooms = _localStorage.getChatRooms();
    final chatRoom = chatRooms.where((room) => room.id == chatRoomId).firstOrNull;
    
    if (chatRoom != null) {
      _chatRoomCache.put(chatRoomId, chatRoom);
      debugPrint('🚀 [PERFORMANCE] Loaded and cached chat room: $chatRoomId');
    }
    
    return chatRoom;
  }

  /// Mark chat room as active for memory management
  void markChatRoomActive(String chatRoomId) {
    _activeChatRooms.add(chatRoomId);
    debugPrint('🚀 [PERFORMANCE] Marked chat room as active: $chatRoomId');
  }

  /// Mark chat room as inactive for memory management
  void markChatRoomInactive(String chatRoomId) {
    _activeChatRooms.remove(chatRoomId);
    debugPrint('🚀 [PERFORMANCE] Marked chat room as inactive: $chatRoomId');
  }

  /// Pre-load data for better performance
  Future<void> preloadChatRoomData(String chatRoomId) async {
    try {
      debugPrint('🚀 [PERFORMANCE] Pre-loading data for chat room: $chatRoomId');
      
      // Pre-load messages
      getOptimizedMessages(chatRoomId);
      
      // Pre-load chat room info
      getOptimizedChatRoom(chatRoomId);
      
      // Mark as active
      markChatRoomActive(chatRoomId);
      
      debugPrint('🚀 [PERFORMANCE] Pre-loading completed for: $chatRoomId');
    } catch (e) {
      debugPrint('❌ [PERFORMANCE] Error pre-loading chat room data: $e');
    }
  }

  /// Batch load multiple chat rooms for efficiency
  Future<void> batchPreloadChatRooms(List<String> chatRoomIds) async {
    try {
      debugPrint('🚀 [PERFORMANCE] Batch pre-loading ${chatRoomIds.length} chat rooms...');
      
      for (final chatRoomId in chatRoomIds) {
        await preloadChatRoomData(chatRoomId);
        
        // Small delay to avoid overwhelming the system
        await Future.delayed(const Duration(milliseconds: 50));
      }
      
      debugPrint('🚀 [PERFORMANCE] Batch pre-loading completed');
    } catch (e) {
      debugPrint('❌ [PERFORMANCE] Error during batch pre-loading: $e');
    }
  }

  /// Track operation for performance monitoring
  void _trackOperation(String operationName) {
    _operationTimestamps[operationName] = DateTime.now();
    _operationCounts[operationName] = (_operationCounts[operationName] ?? 0) + 1;
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'messageCacheSize': _messageCache.length,
      'chatRoomCacheSize': _chatRoomCache.length,
      'activeChatRooms': _activeChatRooms.length,
      'operationCounts': Map.from(_operationCounts),
      'cacheHitRatio': _calculateCacheHitRatio(),
      'memoryUsage': _estimateMemoryUsage(),
    };
  }

  /// Calculate cache hit ratio
  double _calculateCacheHitRatio() {
    final totalOperations = _operationCounts.values.fold(0, (sum, count) => sum + count);
    if (totalOperations == 0) return 0.0;
    
    final cacheHits = _messageCache.hitCount + _chatRoomCache.hitCount;
    return cacheHits / totalOperations;
  }

  /// Estimate memory usage
  String _estimateMemoryUsage() {
    final messageCacheSize = _messageCache.length * 50; // Rough estimate per message
    final chatRoomCacheSize = _chatRoomCache.length * 10; // Rough estimate per chat room
    final totalKB = (messageCacheSize + chatRoomCacheSize) / 1024;
    
    return '${totalKB.toStringAsFixed(1)} KB';
  }

  /// Clear all caches
  void clearCaches() {
    debugPrint('🚀 [PERFORMANCE] Clearing all caches...');
    
    _messageCache.clear();
    _chatRoomCache.clear();
    _activeChatRooms.clear();
    
    debugPrint('🚀 [PERFORMANCE] All caches cleared');
  }

  /// Dispose resources
  void dispose() {
    debugPrint('🚀 [PERFORMANCE] Disposing performance optimizer...');
    
    _memoryCleanupTimer?.cancel();
    _cacheOptimizationTimer?.cancel();
    
    clearCaches();
    
    debugPrint('🚀 [PERFORMANCE] Performance optimizer disposed');
  }
}

/// LRU Cache implementation for efficient memory management
class LRUCache<K, V> {
  final int maxSize;
  final LinkedHashMap<K, V> _cache = LinkedHashMap<K, V>();
  int _hitCount = 0;
  int _missCount = 0;

  LRUCache({required this.maxSize});

  V? get(K key) {
    final value = _cache.remove(key);
    if (value != null) {
      _cache[key] = value; // Move to end (most recently used)
      _hitCount++;
      return value;
    }
    _missCount++;
    return null;
  }

  void put(K key, V value) {
    if (_cache.containsKey(key)) {
      _cache.remove(key);
    } else if (_cache.length >= maxSize) {
      _cache.remove(_cache.keys.first); // Remove least recently used
    }
    _cache[key] = value;
  }

  bool containsKey(K key) => _cache.containsKey(key);
  
  void remove(K key) => _cache.remove(key);
  
  void clear() {
    _cache.clear();
    _hitCount = 0;
    _missCount = 0;
  }

  int get length => _cache.length;
  int get hitCount => _hitCount;
  int get missCount => _missCount;
  Iterable<K> get keys => _cache.keys;
}
