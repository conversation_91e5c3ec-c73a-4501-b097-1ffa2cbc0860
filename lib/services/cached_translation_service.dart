import 'package:flutter/foundation.dart';
import '../services/translation_service.dart';
import '../services/local_storage_service.dart';

class CachedTranslationService {
  static final LocalStorageService _localStorage = LocalStorageService.instance;

  // In-memory cache for faster access during the session
  static final Map<String, String> _memoryCache = {};

  /// Translates text with local caching support
  /// First checks memory cache, then local storage, then API
  static Future<String> translateText({
    required String text,
    required String targetLanguage,
    required String chatRoomId,
    String? sourceLanguage,
  }) async {
    try {
      // Skip translation if text is empty or very short
      if (text.trim().isEmpty || text.trim().length < 2) {
        return text;
      }

      // Create cache key
      final cacheKey = _createCacheKey(text, targetLanguage, chatRoomId);

      // Check memory cache first (fastest)
      if (_memoryCache.containsKey(cacheKey)) {
        debugPrint('🔄 [CACHED_TRANSLATION] Found in memory cache: $cacheKey');
        return _memoryCache[cacheKey]!;
      }

      // Check local storage cache
      final cachedTranslation = _localStorage.getCachedTranslation(
        text,
        targetLanguage,
        chatRoomId,
      );
      if (cachedTranslation != null && cachedTranslation != text) {
        debugPrint('🔄 [CACHED_TRANSLATION] Found in local storage: $cacheKey');
        // Store in memory cache for faster future access
        _memoryCache[cacheKey] = cachedTranslation;
        return cachedTranslation;
      }

      // If not in cache, translate using API
      debugPrint(
        '🔄 [CACHED_TRANSLATION] Translating via API: $text -> $targetLanguage',
      );
      final translatedText = await TranslationService.translateText(
        text: text,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
      );

      // Only cache if translation is different from original
      if (translatedText != text) {
        // Save to local storage
        await _localStorage.saveTranslation(
          text,
          translatedText,
          targetLanguage,
          chatRoomId,
          sourceLanguage: sourceLanguage,
        );

        // Save to memory cache
        _memoryCache[cacheKey] = translatedText;

        debugPrint('🔄 [CACHED_TRANSLATION] Cached translation: $cacheKey');
      }

      return translatedText;
    } catch (e) {
      debugPrint('❌ [CACHED_TRANSLATION] Error translating text: $e');
      return text; // Return original text on error
    }
  }

  /// Batch translate multiple texts with caching
  static Future<List<String>> translateTexts({
    required List<String> texts,
    required String targetLanguage,
    required String chatRoomId,
    String? sourceLanguage,
  }) async {
    final results = <String>[];
    final textsToTranslate = <String>[];
    final indexMap =
        <int, int>{}; // Maps result index to textsToTranslate index

    // Check cache for each text
    for (int i = 0; i < texts.length; i++) {
      final text = texts[i];
      final cacheKey = _createCacheKey(text, targetLanguage, chatRoomId);

      // Check memory cache
      if (_memoryCache.containsKey(cacheKey)) {
        results.add(_memoryCache[cacheKey]!);
        continue;
      }

      // Check local storage
      final cachedTranslation = _localStorage.getCachedTranslation(
        text,
        targetLanguage,
        chatRoomId,
      );
      if (cachedTranslation != null && cachedTranslation != text) {
        _memoryCache[cacheKey] = cachedTranslation;
        results.add(cachedTranslation);
        continue;
      }

      // Need to translate this text
      results.add(''); // Placeholder
      indexMap[i] = textsToTranslate.length;
      textsToTranslate.add(text);
    }

    // Translate uncached texts
    if (textsToTranslate.isNotEmpty) {
      try {
        final translations = await TranslationService.translateTexts(
          texts: textsToTranslate,
          targetLanguage: targetLanguage,
          sourceLanguage: sourceLanguage,
        );

        // Update results and cache
        for (final entry in indexMap.entries) {
          final resultIndex = entry.key;
          final translateIndex = entry.value;

          if (translateIndex < translations.length) {
            final originalText = texts[resultIndex];
            final translatedText = translations[translateIndex];

            results[resultIndex] = translatedText;

            // Cache if different from original
            if (translatedText != originalText) {
              final cacheKey = _createCacheKey(
                originalText,
                targetLanguage,
                chatRoomId,
              );
              _memoryCache[cacheKey] = translatedText;

              // Save to local storage (fire and forget)
              _localStorage.saveTranslation(
                originalText,
                translatedText,
                targetLanguage,
                chatRoomId,
                sourceLanguage: sourceLanguage,
              );
            }
          } else {
            // Fallback to original text
            results[resultIndex] = texts[resultIndex];
          }
        }
      } catch (e) {
        debugPrint('❌ [CACHED_TRANSLATION] Error in batch translation: $e');
        // Fill remaining with original texts
        for (final entry in indexMap.entries) {
          final resultIndex = entry.key;
          results[resultIndex] = texts[resultIndex];
        }
      }
    }

    return results;
  }

  /// Pre-cache translations for a list of messages
  static Future<void> preCacheMessageTranslations({
    required List<String> messageTexts,
    required String targetLanguage,
    required String chatRoomId,
    String? sourceLanguage,
  }) async {
    try {
      debugPrint(
        '🔄 [CACHED_TRANSLATION] Pre-caching ${messageTexts.length} message translations',
      );

      final textsToTranslate = <String>[];

      // Filter out already cached texts
      for (final text in messageTexts) {
        if (text.trim().isEmpty || text.trim().length < 2) continue;

        final cacheKey = _createCacheKey(text, targetLanguage, chatRoomId);

        // Skip if already in memory cache
        if (_memoryCache.containsKey(cacheKey)) continue;

        // Skip if already in local storage
        final cached = _localStorage.getCachedTranslation(
          text,
          targetLanguage,
          chatRoomId,
        );
        if (cached != null && cached != text) {
          _memoryCache[cacheKey] = cached;
          continue;
        }

        textsToTranslate.add(text);
      }

      if (textsToTranslate.isNotEmpty) {
        debugPrint(
          '🔄 [CACHED_TRANSLATION] Translating ${textsToTranslate.length} uncached texts',
        );

        // Translate in smaller batches to avoid API limits
        const batchSize = 5;
        for (int i = 0; i < textsToTranslate.length; i += batchSize) {
          final batch = textsToTranslate.skip(i).take(batchSize).toList();

          final translations = await translateTexts(
            texts: batch,
            targetLanguage: targetLanguage,
            chatRoomId: chatRoomId,
            sourceLanguage: sourceLanguage,
          );

          debugPrint(
            '🔄 [CACHED_TRANSLATION] Pre-cached batch of ${translations.length} translations',
          );

          // Small delay to avoid overwhelming the API
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      debugPrint('🔄 [CACHED_TRANSLATION] Pre-caching completed');
    } catch (e) {
      debugPrint('❌ [CACHED_TRANSLATION] Error in pre-caching: $e');
    }
  }

  /// Clear memory cache
  static void clearMemoryCache() {
    _memoryCache.clear();
    debugPrint('🔄 [CACHED_TRANSLATION] Memory cache cleared');
  }

  /// Clear cache for specific chat room
  static void clearChatRoomCache(String chatRoomId) {
    final keysToRemove = <String>[];
    for (final key in _memoryCache.keys) {
      if (key.startsWith('${chatRoomId}_')) {
        keysToRemove.add(key);
      }
    }

    for (final key in keysToRemove) {
      _memoryCache.remove(key);
    }

    debugPrint(
      '🔄 [CACHED_TRANSLATION] Cleared cache for chat room: $chatRoomId',
    );
  }

  /// Optimize cache for better performance
  static void optimizeCache() {
    try {
      debugPrint('🔄 [CACHED_TRANSLATION] Optimizing cache...');

      // Clear old memory cache entries (keep only recent ones)
      if (_memoryCache.length > 100) {
        final keysToRemove =
            _memoryCache.keys.take(_memoryCache.length - 50).toList();
        for (final key in keysToRemove) {
          _memoryCache.remove(key);
        }
        debugPrint(
          '🔄 [CACHED_TRANSLATION] Removed ${keysToRemove.length} old memory cache entries',
        );
      }

      // Clear old persistent translations
      _localStorage.clearOldTranslations();

      debugPrint('🔄 [CACHED_TRANSLATION] Cache optimization completed');
    } catch (e) {
      debugPrint('❌ [CACHED_TRANSLATION] Error optimizing cache: $e');
    }
  }

  /// Get cache statistics
  static Map<String, dynamic> getCacheStats() {
    return {
      'memoryCacheSize': _memoryCache.length,
      'memoryCacheKeys': _memoryCache.keys.take(10).toList(), // Sample of keys
    };
  }

  /// Create cache key for consistent caching
  static String _createCacheKey(
    String text,
    String targetLanguage,
    String chatRoomId,
  ) {
    return '${chatRoomId}_${targetLanguage}_${text.hashCode}';
  }

  /// Detect language with caching
  static Future<String?> detectLanguage(String text) async {
    // For language detection, we can use a simple cache based on text hash
    // since the same text will always have the same language
    final cacheKey = 'detect_${text.hashCode}';

    if (_memoryCache.containsKey(cacheKey)) {
      return _memoryCache[cacheKey];
    }

    try {
      final detectedLanguage = await TranslationService.detectLanguage(text);
      if (detectedLanguage != null) {
        _memoryCache[cacheKey] = detectedLanguage;
      }
      return detectedLanguage;
    } catch (e) {
      debugPrint('❌ [CACHED_TRANSLATION] Error detecting language: $e');
      return null;
    }
  }

  /// Check if text is likely English (from original service)
  static bool isLikelyEnglish(String text) {
    return TranslationService.isLikelyEnglish(text);
  }
}
