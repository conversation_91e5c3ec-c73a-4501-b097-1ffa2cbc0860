import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/sync_manager.dart';
import '../services/local_storage_service.dart';
import '../services/cached_translation_service.dart';
import '../providers/user_provider.dart';
import '../models/chat_models.dart';

class BackgroundDataLoader {
  static BackgroundDataLoader? _instance;
  static BackgroundDataLoader get instance => _instance ??= BackgroundDataLoader._();

  BackgroundDataLoader._();

  final SyncManager _syncManager = SyncManager.instance;
  final LocalStorageService _localStorage = LocalStorageService.instance;

  bool _isInitialized = false;
  bool _isLoading = false;
  final List<String> _loadingSteps = [];
  final StreamController<BackgroundLoadingState> _stateController = 
      StreamController<BackgroundLoadingState>.broadcast();

  /// Get loading state stream
  Stream<BackgroundLoadingState> get loadingStateStream => _stateController.stream;

  /// Get current loading state
  BackgroundLoadingState get currentState => BackgroundLoadingState(
    isLoading: _isLoading,
    isInitialized: _isInitialized,
    currentStep: _loadingSteps.isNotEmpty ? _loadingSteps.last : null,
    completedSteps: List.from(_loadingSteps),
  );

  /// Initialize background data loading
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔄 [BACKGROUND_LOADER] Starting background data loading...');
    
    _isLoading = true;
    _emitState();

    try {
      // Step 1: Initialize local storage and sync manager
      await _loadStep('Initializing local storage...', () async {
        await _localStorage.initialize();
        await _syncManager.initialize();
      });

      // Step 2: Pre-load cached chat rooms
      await _loadStep('Loading cached conversations...', () async {
        final cachedChatRooms = _localStorage.getChatRooms();
        debugPrint('🔄 [BACKGROUND_LOADER] Pre-loaded ${cachedChatRooms.length} cached chat rooms');
      });

      // Step 3: Pre-load recent messages for quick access
      await _loadStep('Loading recent messages...', () async {
        await _preloadRecentMessages();
      });

      // Step 4: Start background sync
      await _loadStep('Starting real-time sync...', () async {
        // Sync manager is already initialized and will handle real-time updates
        debugPrint('🔄 [BACKGROUND_LOADER] Real-time sync is active');
      });

      _isInitialized = true;
      debugPrint('🔄 [BACKGROUND_LOADER] Background data loading completed');
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error during background loading: $e');
    } finally {
      _isLoading = false;
      _emitState();
    }
  }

  /// Pre-load recent messages for faster chat opening
  Future<void> _preloadRecentMessages() async {
    try {
      final chatRooms = _localStorage.getChatRooms();
      
      for (final chatRoom in chatRooms.take(5)) { // Pre-load top 5 chat rooms
        final messages = _localStorage.getLatestMessages(chatRoom.id, limit: 15);
        debugPrint('🔄 [BACKGROUND_LOADER] Pre-loaded ${messages.length} messages for chat: ${chatRoom.id}');
        
        // Small delay to avoid overwhelming the system
        await Future.delayed(const Duration(milliseconds: 50));
      }
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error pre-loading messages: $e');
    }
  }

  /// Pre-cache translations for better performance
  Future<void> preCacheTranslations({
    required String targetLanguage,
    required UserProvider userProvider,
  }) async {
    if (_isLoading) return; // Don't interfere with initial loading

    try {
      debugPrint('🔄 [BACKGROUND_LOADER] Starting translation pre-caching...');
      
      final chatRooms = _localStorage.getChatRooms();
      
      for (final chatRoom in chatRooms.take(3)) { // Pre-cache top 3 chat rooms
        final messages = _localStorage.getLatestMessages(chatRoom.id, limit: 10);
        final messageTexts = messages
            .where((msg) => msg.text != null && msg.text!.isNotEmpty)
            .map((msg) => msg.text!)
            .toList();
        
        if (messageTexts.isNotEmpty) {
          await CachedTranslationService.preCacheMessageTranslations(
            messageTexts: messageTexts,
            targetLanguage: targetLanguage,
            chatRoomId: chatRoom.id,
          );
          
          debugPrint('🔄 [BACKGROUND_LOADER] Pre-cached translations for chat: ${chatRoom.id}');
        }
        
        // Small delay between chat rooms
        await Future.delayed(const Duration(milliseconds: 200));
      }
      
      debugPrint('🔄 [BACKGROUND_LOADER] Translation pre-caching completed');
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error pre-caching translations: $e');
    }
  }

  /// Load a step with progress tracking
  Future<void> _loadStep(String stepName, Future<void> Function() stepFunction) async {
    debugPrint('🔄 [BACKGROUND_LOADER] $stepName');
    _loadingSteps.add(stepName);
    _emitState();
    
    try {
      await stepFunction();
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error in step "$stepName": $e');
      rethrow;
    }
  }

  /// Emit current state to listeners
  void _emitState() {
    _stateController.add(currentState);
  }

  /// Warm up chat room for instant loading
  Future<void> warmUpChatRoom(String chatRoomId) async {
    try {
      debugPrint('🔄 [BACKGROUND_LOADER] Warming up chat room: $chatRoomId');
      
      // Start sync for this chat room if not already active
      if (!_syncManager.isMessageSyncActive(chatRoomId)) {
        _syncManager.startMessageSync(chatRoomId);
      }
      
      // Pre-load messages from local storage
      final messages = _localStorage.getLatestMessages(chatRoomId, limit: 15);
      debugPrint('🔄 [BACKGROUND_LOADER] Warmed up ${messages.length} messages for chat: $chatRoomId');
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error warming up chat room: $e');
    }
  }

  /// Pre-load data for specific user
  Future<void> preloadUserData({
    required String userId,
    required String targetLanguage,
  }) async {
    try {
      debugPrint('🔄 [BACKGROUND_LOADER] Pre-loading data for user: $userId');
      
      // This can be expanded to pre-load user-specific data
      // For now, we'll focus on translation pre-caching
      
      debugPrint('🔄 [BACKGROUND_LOADER] User data pre-loading completed');
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error pre-loading user data: $e');
    }
  }

  /// Get loading progress (0.0 to 1.0)
  double get loadingProgress {
    if (!_isLoading) return _isInitialized ? 1.0 : 0.0;
    
    const totalSteps = 4; // Total number of loading steps
    return _loadingSteps.length / totalSteps;
  }

  /// Check if specific step is completed
  bool isStepCompleted(String stepName) {
    return _loadingSteps.contains(stepName);
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'isInitialized': _isInitialized,
      'isLoading': _isLoading,
      'completedSteps': _loadingSteps.length,
      'loadingProgress': loadingProgress,
      'syncManagerStatus': _syncManager.getSyncStatus(),
      'translationCacheStats': CachedTranslationService.getCacheStats(),
    };
  }

  /// Force refresh all data
  Future<void> forceRefresh() async {
    try {
      debugPrint('🔄 [BACKGROUND_LOADER] Force refreshing all data...');
      
      _isLoading = true;
      _emitState();
      
      await _syncManager.forceRefresh();
      
      debugPrint('🔄 [BACKGROUND_LOADER] Force refresh completed');
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error during force refresh: $e');
    } finally {
      _isLoading = false;
      _emitState();
    }
  }

  /// Dispose resources
  void dispose() {
    debugPrint('🔄 [BACKGROUND_LOADER] Disposing background data loader...');
    _stateController.close();
  }
}

/// Background loading state
class BackgroundLoadingState {
  final bool isLoading;
  final bool isInitialized;
  final String? currentStep;
  final List<String> completedSteps;

  const BackgroundLoadingState({
    required this.isLoading,
    required this.isInitialized,
    this.currentStep,
    required this.completedSteps,
  });

  double get progress {
    const totalSteps = 4;
    return completedSteps.length / totalSteps;
  }

  @override
  String toString() {
    return 'BackgroundLoadingState(isLoading: $isLoading, isInitialized: $isInitialized, currentStep: $currentStep, progress: ${(progress * 100).toStringAsFixed(1)}%)';
  }
}
