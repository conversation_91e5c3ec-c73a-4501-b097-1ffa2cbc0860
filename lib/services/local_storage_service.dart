import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../models/local_storage_models.dart';
import '../models/chat_models.dart';

class LocalStorageService {
  static const String _messagesBoxName = 'messages';
  static const String _chatRoomsBoxName = 'chat_rooms';
  static const String _translationsBoxName = 'translations';
  static const String _syncMetadataBoxName = 'sync_metadata';

  static Box<LocalMessage>? _messagesBox;
  static Box<LocalChatRoom>? _chatRoomsBox;
  static Box<LocalTranslation>? _translationsBox;
  static Box<Map<String, dynamic>>? _syncMetadataBox;

  static LocalStorageService? _instance;
  static LocalStorageService get instance =>
      _instance ??= LocalStorageService._();

  LocalStorageService._();

  /// Initialize all Hive boxes
  Future<void> initialize() async {
    try {
      debugPrint('🗄️ [LOCAL_STORAGE] Initializing local storage...');

      _messagesBox = await Hive.openBox<LocalMessage>(_messagesBoxName);
      _chatRoomsBox = await Hive.openBox<LocalChatRoom>(_chatRoomsBoxName);
      _translationsBox = await Hive.openBox<LocalTranslation>(
        _translationsBoxName,
      );
      _syncMetadataBox = await Hive.openBox<Map<String, dynamic>>(
        _syncMetadataBoxName,
      );

      debugPrint('🗄️ [LOCAL_STORAGE] Local storage initialized successfully');
      debugPrint('🗄️ [LOCAL_STORAGE] Messages: ${_messagesBox!.length}');
      debugPrint('🗄️ [LOCAL_STORAGE] Chat rooms: ${_chatRoomsBox!.length}');
      debugPrint(
        '🗄️ [LOCAL_STORAGE] Translations: ${_translationsBox!.length}',
      );
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error initializing local storage: $e');
      rethrow;
    }
  }

  /// Close all boxes
  Future<void> close() async {
    await _messagesBox?.close();
    await _chatRoomsBox?.close();
    await _translationsBox?.close();
    await _syncMetadataBox?.close();
  }

  /// Clear all local data (for logout)
  Future<void> clearAll() async {
    try {
      await _messagesBox?.clear();
      await _chatRoomsBox?.clear();
      await _translationsBox?.clear();
      await _syncMetadataBox?.clear();
      debugPrint('🗄️ [LOCAL_STORAGE] All local data cleared');
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error clearing local data: $e');
    }
  }

  // ==================== CHAT ROOMS ====================

  /// Save chat room to local storage
  Future<void> saveChatRoom(ChatRoom chatRoom) async {
    try {
      final localChatRoom = LocalChatRoom.fromChatRoom(chatRoom);
      await _chatRoomsBox!.put(chatRoom.id, localChatRoom);
      debugPrint('🗄️ [LOCAL_STORAGE] Saved chat room: ${chatRoom.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error saving chat room: $e');
    }
  }

  /// Save multiple chat rooms
  Future<void> saveChatRooms(List<ChatRoom> chatRooms) async {
    try {
      final Map<String, LocalChatRoom> localChatRooms = {};
      for (final chatRoom in chatRooms) {
        localChatRooms[chatRoom.id] = LocalChatRoom.fromChatRoom(chatRoom);
      }
      await _chatRoomsBox!.putAll(localChatRooms);
      debugPrint('🗄️ [LOCAL_STORAGE] Saved ${chatRooms.length} chat rooms');
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error saving chat rooms: $e');
    }
  }

  /// Get all chat rooms from local storage
  List<ChatRoom> getChatRooms() {
    try {
      final localChatRooms = _chatRoomsBox!.values.toList();
      // Sort by last message time (newest first)
      localChatRooms.sort((a, b) {
        if (a.lastMessageTime == null && b.lastMessageTime == null) return 0;
        if (a.lastMessageTime == null) return 1;
        if (b.lastMessageTime == null) return -1;
        return b.lastMessageTime!.compareTo(a.lastMessageTime!);
      });

      return localChatRooms.map((local) => local.toChatRoom()).toList();
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error getting chat rooms: $e');
      return [];
    }
  }

  /// Get specific chat room
  ChatRoom? getChatRoom(String chatRoomId) {
    try {
      final localChatRoom = _chatRoomsBox!.get(chatRoomId);
      return localChatRoom?.toChatRoom();
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error getting chat room: $e');
      return null;
    }
  }

  /// Update chat room unread count
  Future<void> updateChatRoomUnreadCount(
    String chatRoomId,
    Map<String, int> unreadCount,
  ) async {
    try {
      final localChatRoom = _chatRoomsBox!.get(chatRoomId);
      if (localChatRoom != null) {
        final updated = localChatRoom.copyWith(
          unreadCount: unreadCount,
          lastUpdated: DateTime.now(),
        );
        await _chatRoomsBox!.put(chatRoomId, updated);
        debugPrint(
          '🗄️ [LOCAL_STORAGE] Updated unread count for chat room: $chatRoomId',
        );
      }
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error updating unread count: $e');
    }
  }

  // ==================== MESSAGES ====================

  /// Save message to local storage
  Future<void> saveMessage(Message message) async {
    try {
      final localMessage = LocalMessage.fromMessage(message);
      await _messagesBox!.put(message.id, localMessage);
      debugPrint('🗄️ [LOCAL_STORAGE] Saved message: ${message.id}');
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error saving message: $e');
    }
  }

  /// Save multiple messages
  Future<void> saveMessages(List<Message> messages) async {
    try {
      final Map<String, LocalMessage> localMessages = {};
      for (final message in messages) {
        localMessages[message.id] = LocalMessage.fromMessage(message);
      }
      await _messagesBox!.putAll(localMessages);
      debugPrint('🗄️ [LOCAL_STORAGE] Saved ${messages.length} messages');
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error saving messages: $e');
    }
  }

  /// Get messages for a chat room with pagination
  List<Message> getMessages(
    String chatRoomId, {
    int limit = 15,
    DateTime? before,
  }) {
    try {
      final allMessages =
          _messagesBox!.values
              .where((msg) => msg.chatRoomId == chatRoomId)
              .toList();

      // Sort by timestamp (newest first)
      allMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // Apply before filter if provided
      List<LocalMessage> filteredMessages = allMessages;
      if (before != null) {
        filteredMessages =
            allMessages.where((msg) => msg.timestamp.isBefore(before)).toList();
      }

      // Apply limit
      final limitedMessages = filteredMessages.take(limit).toList();

      return limitedMessages.map((local) => local.toMessage()).toList();
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error getting messages: $e');
      return [];
    }
  }

  /// Get latest messages for a chat room
  List<Message> getLatestMessages(String chatRoomId, {int limit = 15}) {
    return getMessages(chatRoomId, limit: limit);
  }

  /// Update message status
  Future<void> updateMessageStatus(
    String messageId,
    MessageStatus status,
  ) async {
    try {
      final localMessage = _messagesBox!.get(messageId);
      if (localMessage != null) {
        final updated = localMessage.copyWith(
          status: status.index,
          lastUpdated: DateTime.now(),
        );
        await _messagesBox!.put(messageId, updated);
        debugPrint(
          '🗄️ [LOCAL_STORAGE] Updated message status: $messageId -> $status',
        );
      }
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error updating message status: $e');
    }
  }

  /// Update message read status
  Future<void> updateMessageReadBy(
    String messageId,
    List<String> readBy,
  ) async {
    try {
      final localMessage = _messagesBox!.get(messageId);
      if (localMessage != null) {
        final updated = localMessage.copyWith(
          readBy: readBy,
          lastUpdated: DateTime.now(),
        );
        await _messagesBox!.put(messageId, updated);
        debugPrint('🗄️ [LOCAL_STORAGE] Updated message read by: $messageId');
      }
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error updating message read by: $e');
    }
  }

  // ==================== TRANSLATIONS ====================

  /// Save translation to local storage
  Future<void> saveTranslation(
    String originalText,
    String translatedText,
    String targetLanguage,
    String chatRoomId, {
    String? sourceLanguage,
  }) async {
    try {
      final translation = LocalTranslation(
        originalText: originalText,
        translatedText: translatedText,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        createdAt: DateTime.now(),
        chatRoomId: chatRoomId,
      );

      await _translationsBox!.put(translation.cacheKey, translation);
      debugPrint(
        '🗄️ [LOCAL_STORAGE] Saved translation: ${translation.cacheKey}',
      );
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error saving translation: $e');
    }
  }

  /// Get cached translation
  String? getCachedTranslation(
    String originalText,
    String targetLanguage,
    String chatRoomId,
  ) {
    try {
      final cacheKey =
          '${chatRoomId}_${targetLanguage}_${originalText.hashCode}';
      final translation = _translationsBox!.get(cacheKey);
      return translation?.translatedText;
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error getting cached translation: $e');
      return null;
    }
  }

  /// Clear old translations (older than 30 days)
  Future<void> clearOldTranslations() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final keysToDelete = <String>[];

      for (final entry in _translationsBox!.toMap().entries) {
        if (entry.value.createdAt.isBefore(cutoffDate)) {
          keysToDelete.add(entry.key);
        }
      }

      await _translationsBox!.deleteAll(keysToDelete);
      debugPrint(
        '🗄️ [LOCAL_STORAGE] Cleared ${keysToDelete.length} old translations',
      );
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error clearing old translations: $e');
    }
  }

  // ==================== SYNC METADATA ====================

  /// Save sync metadata
  Future<void> saveSyncMetadata(
    String key,
    Map<String, dynamic> metadata,
  ) async {
    try {
      await _syncMetadataBox!.put(key, metadata);
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error saving sync metadata: $e');
    }
  }

  /// Get sync metadata
  Map<String, dynamic>? getSyncMetadata(String key) {
    try {
      return _syncMetadataBox!.get(key);
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error getting sync metadata: $e');
      return null;
    }
  }

  /// Optimize cache for better performance
  void optimizeCache() {
    try {
      debugPrint('💾 [LOCAL_STORAGE] Optimizing cache...');

      // Compact Hive boxes to reclaim space
      _messagesBox?.compact();
      _chatRoomsBox?.compact();
      _translationsBox?.compact();
      _syncMetadataBox?.compact();

      debugPrint('💾 [LOCAL_STORAGE] Cache optimization completed');
    } catch (e) {
      debugPrint('❌ [LOCAL_STORAGE] Error optimizing cache: $e');
    }
  }
}
