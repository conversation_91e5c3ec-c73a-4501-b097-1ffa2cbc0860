import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';
import '../services/local_storage_service.dart';

class SyncManager {
  static SyncManager? _instance;
  static SyncManager get instance => _instance ??= SyncManager._();

  SyncManager._();

  final ChatService _chatService = ChatService();
  final LocalStorageService _localStorage = LocalStorageService.instance;

  // Stream subscriptions for real-time updates
  final Map<String, StreamSubscription<List<Message>>?> _messageStreams = {};
  final Map<String, StreamSubscription<ChatRoom>?> _chatRoomStreams = {};
  StreamSubscription<List<ChatRoom>>? _chatRoomsListStream;

  // Controllers for local data streams
  final Map<String, StreamController<List<Message>>> _messageControllers = {};
  final StreamController<List<ChatRoom>> _chatRoomsController = StreamController<List<ChatRoom>>.broadcast();

  bool _isInitialized = false;

  /// Initialize sync manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔄 [SYNC_MANAGER] Initializing sync manager...');
    
    // Start listening to chat rooms list
    _startChatRoomsListSync();
    
    _isInitialized = true;
    debugPrint('🔄 [SYNC_MANAGER] Sync manager initialized');
  }

  /// Dispose all streams
  void dispose() {
    debugPrint('🔄 [SYNC_MANAGER] Disposing sync manager...');
    
    // Cancel all message streams
    for (final subscription in _messageStreams.values) {
      subscription?.cancel();
    }
    _messageStreams.clear();

    // Cancel all chat room streams
    for (final subscription in _chatRoomStreams.values) {
      subscription?.cancel();
    }
    _chatRoomStreams.clear();

    // Cancel chat rooms list stream
    _chatRoomsListStream?.cancel();

    // Close all controllers
    for (final controller in _messageControllers.values) {
      controller.close();
    }
    _messageControllers.clear();
    _chatRoomsController.close();

    _isInitialized = false;
    debugPrint('🔄 [SYNC_MANAGER] Sync manager disposed');
  }

  // ==================== CHAT ROOMS SYNC ====================

  /// Start syncing chat rooms list
  void _startChatRoomsListSync() {
    debugPrint('🔄 [SYNC_MANAGER] Starting chat rooms list sync...');
    
    // First, emit cached data immediately
    final cachedChatRooms = _localStorage.getChatRooms();
    _chatRoomsController.add(cachedChatRooms);
    debugPrint('🔄 [SYNC_MANAGER] Emitted ${cachedChatRooms.length} cached chat rooms');

    // Then start listening to real-time updates
    _chatRoomsListStream = _chatService.getChatRooms().listen(
      (chatRooms) async {
        debugPrint('🔄 [SYNC_MANAGER] Received ${chatRooms.length} chat rooms from Firestore');
        
        // Save to local storage
        await _localStorage.saveChatRooms(chatRooms);
        
        // Emit updated data
        _chatRoomsController.add(chatRooms);
        
        debugPrint('🔄 [SYNC_MANAGER] Chat rooms synced and emitted');
      },
      onError: (error) {
        debugPrint('❌ [SYNC_MANAGER] Error in chat rooms stream: $error');
        // On error, emit cached data
        final cachedChatRooms = _localStorage.getChatRooms();
        _chatRoomsController.add(cachedChatRooms);
      },
    );
  }

  /// Get chat rooms stream (local + real-time)
  Stream<List<ChatRoom>> getChatRoomsStream() {
    if (!_isInitialized) {
      throw StateError('SyncManager not initialized. Call initialize() first.');
    }
    return _chatRoomsController.stream;
  }

  /// Update chat room unread count locally and sync
  Future<void> updateChatRoomUnreadCount(String chatRoomId, Map<String, int> unreadCount) async {
    try {
      // Update local storage immediately
      await _localStorage.updateChatRoomUnreadCount(chatRoomId, unreadCount);
      
      // Emit updated chat rooms list
      final updatedChatRooms = _localStorage.getChatRooms();
      _chatRoomsController.add(updatedChatRooms);
      
      debugPrint('🔄 [SYNC_MANAGER] Updated unread count locally for chat room: $chatRoomId');
    } catch (e) {
      debugPrint('❌ [SYNC_MANAGER] Error updating unread count: $e');
    }
  }

  // ==================== MESSAGES SYNC ====================

  /// Start syncing messages for a chat room
  void startMessageSync(String chatRoomId) {
    if (_messageStreams.containsKey(chatRoomId)) {
      debugPrint('🔄 [SYNC_MANAGER] Message sync already active for chat room: $chatRoomId');
      return;
    }

    debugPrint('🔄 [SYNC_MANAGER] Starting message sync for chat room: $chatRoomId');

    // Create controller for this chat room
    _messageControllers[chatRoomId] = StreamController<List<Message>>.broadcast();

    // First, emit cached messages immediately
    final cachedMessages = _localStorage.getLatestMessages(chatRoomId, limit: 15);
    _messageControllers[chatRoomId]!.add(cachedMessages);
    debugPrint('🔄 [SYNC_MANAGER] Emitted ${cachedMessages.length} cached messages for chat room: $chatRoomId');

    // Then start listening to real-time updates
    _messageStreams[chatRoomId] = _chatService
        .getMessagesPaginated(chatRoomId, limit: 15)
        .listen(
      (messages) async {
        debugPrint('🔄 [SYNC_MANAGER] Received ${messages.length} messages from Firestore for chat room: $chatRoomId');
        
        // Save to local storage
        await _localStorage.saveMessages(messages);
        
        // Emit updated messages
        _messageControllers[chatRoomId]?.add(messages);
        
        debugPrint('🔄 [SYNC_MANAGER] Messages synced and emitted for chat room: $chatRoomId');
      },
      onError: (error) {
        debugPrint('❌ [SYNC_MANAGER] Error in messages stream for chat room $chatRoomId: $error');
        // On error, emit cached messages
        final cachedMessages = _localStorage.getLatestMessages(chatRoomId, limit: 15);
        _messageControllers[chatRoomId]?.add(cachedMessages);
      },
    );
  }

  /// Stop syncing messages for a chat room
  void stopMessageSync(String chatRoomId) {
    debugPrint('🔄 [SYNC_MANAGER] Stopping message sync for chat room: $chatRoomId');
    
    _messageStreams[chatRoomId]?.cancel();
    _messageStreams.remove(chatRoomId);
    
    _messageControllers[chatRoomId]?.close();
    _messageControllers.remove(chatRoomId);
  }

  /// Get messages stream for a chat room (local + real-time)
  Stream<List<Message>>? getMessagesStream(String chatRoomId) {
    return _messageControllers[chatRoomId]?.stream;
  }

  /// Add message optimistically (for immediate UI update)
  Future<void> addMessageOptimistically(String chatRoomId, Message message) async {
    try {
      // Save to local storage immediately
      await _localStorage.saveMessage(message);
      
      // Get updated messages and emit
      final updatedMessages = _localStorage.getLatestMessages(chatRoomId, limit: 15);
      _messageControllers[chatRoomId]?.add(updatedMessages);
      
      debugPrint('🔄 [SYNC_MANAGER] Added message optimistically: ${message.id}');
    } catch (e) {
      debugPrint('❌ [SYNC_MANAGER] Error adding message optimistically: $e');
    }
  }

  /// Update message status locally
  Future<void> updateMessageStatus(String messageId, MessageStatus status) async {
    try {
      await _localStorage.updateMessageStatus(messageId, status);
      debugPrint('🔄 [SYNC_MANAGER] Updated message status locally: $messageId -> $status');
    } catch (e) {
      debugPrint('❌ [SYNC_MANAGER] Error updating message status: $e');
    }
  }

  /// Update message read status locally
  Future<void> updateMessageReadBy(String messageId, List<String> readBy) async {
    try {
      await _localStorage.updateMessageReadBy(messageId, readBy);
      debugPrint('🔄 [SYNC_MANAGER] Updated message read by locally: $messageId');
    } catch (e) {
      debugPrint('❌ [SYNC_MANAGER] Error updating message read by: $e');
    }
  }

  /// Load more messages for pagination
  Future<List<Message>> loadMoreMessages(String chatRoomId, DateTime before, {int limit = 15}) async {
    try {
      // First try to get from local storage
      final localMessages = _localStorage.getMessages(chatRoomId, limit: limit, before: before);
      
      if (localMessages.isNotEmpty) {
        debugPrint('🔄 [SYNC_MANAGER] Loaded ${localMessages.length} more messages from local storage');
        return localMessages;
      }

      // If not enough local messages, fetch from Firestore
      final remoteMessages = await _chatService.loadMoreMessages(chatRoomId, before, limit: limit);
      
      if (remoteMessages.isNotEmpty) {
        // Save to local storage
        await _localStorage.saveMessages(remoteMessages);
        debugPrint('🔄 [SYNC_MANAGER] Loaded ${remoteMessages.length} more messages from Firestore');
      }
      
      return remoteMessages;
    } catch (e) {
      debugPrint('❌ [SYNC_MANAGER] Error loading more messages: $e');
      return [];
    }
  }

  // ==================== UTILITY METHODS ====================

  /// Force refresh data from Firestore
  Future<void> forceRefresh() async {
    try {
      debugPrint('🔄 [SYNC_MANAGER] Force refreshing data...');
      
      // This will trigger the streams to emit fresh data
      // The streams will automatically save to local storage
      
      debugPrint('🔄 [SYNC_MANAGER] Force refresh completed');
    } catch (e) {
      debugPrint('❌ [SYNC_MANAGER] Error during force refresh: $e');
    }
  }

  /// Check if chat room sync is active
  bool isMessageSyncActive(String chatRoomId) {
    return _messageStreams.containsKey(chatRoomId);
  }

  /// Get sync status
  Map<String, dynamic> getSyncStatus() {
    return {
      'isInitialized': _isInitialized,
      'activeChatRoomSyncs': _messageStreams.keys.toList(),
      'chatRoomsListSyncActive': _chatRoomsListStream != null,
    };
  }
}
