import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/chat_models.dart';
import '../services/sync_manager.dart';
import '../services/local_storage_service.dart';

class ChatListProvider extends ChangeNotifier {
  final SyncManager _syncManager = SyncManager.instance;
  final LocalStorageService _localStorage = LocalStorageService.instance;

  List<ChatRoom> _chatRooms = [];
  bool _isLoading = true;
  bool _isInitialized = false;
  StreamSubscription<List<ChatRoom>>? _chatRoomsSubscription;

  // Getters
  List<ChatRoom> get chatRooms => _chatRooms;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;

  /// Initialize the chat list provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔄 [CHAT_LIST_PROVIDER] Initializing...');

    try {
      // Load cached chat rooms immediately for instant UI
      _loadCachedChatRooms();

      // Start listening to sync manager's chat rooms stream
      _startChatRoomsStream();

      _isInitialized = true;
      debugPrint('🔄 [CHAT_LIST_PROVIDER] Initialized successfully');
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error during initialization: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load cached chat rooms for immediate display
  void _loadCachedChatRooms() {
    try {
      final cachedChatRooms = _localStorage.getChatRooms();
      if (cachedChatRooms.isNotEmpty) {
        _chatRooms = cachedChatRooms;
        _isLoading = false;
        notifyListeners();
        debugPrint(
          '🔄 [CHAT_LIST_PROVIDER] Loaded ${cachedChatRooms.length} cached chat rooms',
        );
      }
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error loading cached chat rooms: $e');
    }
  }

  /// Start listening to sync manager's chat rooms stream
  void _startChatRoomsStream() {
    try {
      _chatRoomsSubscription = _syncManager.getChatRoomsStream().listen(
        (chatRooms) {
          debugPrint(
            '🔄 [CHAT_LIST_PROVIDER] Received ${chatRooms.length} chat rooms from sync manager',
          );

          // Update chat rooms with smooth animation support
          _updateChatRoomsWithAnimation(chatRooms);
        },
        onError: (error) {
          debugPrint(
            '❌ [CHAT_LIST_PROVIDER] Error in chat rooms stream: $error',
          );
          _isLoading = false;
          notifyListeners();
        },
      );
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error starting chat rooms stream: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update chat rooms with animation support
  void _updateChatRoomsWithAnimation(List<ChatRoom> newChatRooms) {
    try {
      // Sort chat rooms by last message timestamp (most recent first)
      newChatRooms.sort((a, b) {
        final aTime =
            a.lastMessageTime ?? DateTime.fromMillisecondsSinceEpoch(0);
        final bTime =
            b.lastMessageTime ?? DateTime.fromMillisecondsSinceEpoch(0);
        return bTime.compareTo(aTime);
      });

      // Check if there are actual changes to avoid unnecessary UI updates
      if (_chatRoomsAreEqual(_chatRooms, newChatRooms)) {
        debugPrint(
          '🔄 [CHAT_LIST_PROVIDER] No changes in chat rooms, skipping update',
        );
        return;
      }

      _chatRooms = newChatRooms;
      _isLoading = false;
      notifyListeners();

      debugPrint(
        '🔄 [CHAT_LIST_PROVIDER] Updated chat rooms list with ${newChatRooms.length} items',
      );
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error updating chat rooms: $e');
    }
  }

  /// Check if two chat room lists are equal (to avoid unnecessary updates)
  bool _chatRoomsAreEqual(List<ChatRoom> list1, List<ChatRoom> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      final room1 = list1[i];
      final room2 = list2[i];

      if (room1.id != room2.id ||
          room1.lastMessage != room2.lastMessage ||
          room1.lastMessageTime != room2.lastMessageTime ||
          !_unreadCountsAreEqual(room1.unreadCount, room2.unreadCount)) {
        return false;
      }
    }

    return true;
  }

  /// Check if unread counts are equal
  bool _unreadCountsAreEqual(
    Map<String, int>? count1,
    Map<String, int>? count2,
  ) {
    if (count1 == null && count2 == null) return true;
    if (count1 == null || count2 == null) return false;
    if (count1.length != count2.length) return false;

    for (final entry in count1.entries) {
      if (count2[entry.key] != entry.value) return false;
    }

    return true;
  }

  /// Update unread count for a specific chat room
  Future<void> updateUnreadCount(
    String chatRoomId,
    Map<String, int> unreadCount,
  ) async {
    try {
      // Update via sync manager (handles local storage + Firestore sync)
      await _syncManager.updateChatRoomUnreadCount(chatRoomId, unreadCount);

      debugPrint(
        '🔄 [CHAT_LIST_PROVIDER] Updated unread count for chat room: $chatRoomId',
      );
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error updating unread count: $e');
    }
  }

  /// Mark chat room as read (set unread count to 0)
  Future<void> markChatRoomAsRead(String chatRoomId, String userId) async {
    try {
      await updateUnreadCount(chatRoomId, {userId: 0});
      debugPrint(
        '🔄 [CHAT_LIST_PROVIDER] Marked chat room as read: $chatRoomId',
      );
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error marking chat room as read: $e');
    }
  }

  /// Get unread count for a specific chat room and user
  int getUnreadCount(String chatRoomId, String userId) {
    try {
      final chatRoom = _chatRooms.firstWhere((room) => room.id == chatRoomId);
      return chatRoom.unreadCount[userId] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Get total unread count across all chat rooms for a user
  int getTotalUnreadCount(String userId) {
    int total = 0;
    for (final chatRoom in _chatRooms) {
      total += chatRoom.unreadCount[userId] ?? 0;
    }
    return total;
  }

  /// Refresh chat rooms (force reload from Firestore)
  Future<void> refresh() async {
    try {
      debugPrint('🔄 [CHAT_LIST_PROVIDER] Refreshing chat rooms...');

      _isLoading = true;
      notifyListeners();

      // Force refresh via sync manager
      await _syncManager.forceRefresh();

      debugPrint('🔄 [CHAT_LIST_PROVIDER] Refresh completed');
    } catch (e) {
      debugPrint('❌ [CHAT_LIST_PROVIDER] Error during refresh: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Search chat rooms by name or last message
  List<ChatRoom> searchChatRooms(String query) {
    if (query.trim().isEmpty) return _chatRooms;

    final lowercaseQuery = query.toLowerCase();
    return _chatRooms.where((chatRoom) {
      // Search in chat room name (use groupName for group chats)
      final nameMatch =
          chatRoom.groupName?.toLowerCase().contains(lowercaseQuery) ?? false;

      // Search in last message
      final messageMatch =
          chatRoom.lastMessage?.toLowerCase().contains(lowercaseQuery) ?? false;

      return nameMatch || messageMatch;
    }).toList();
  }

  /// Get chat room by ID
  ChatRoom? getChatRoomById(String chatRoomId) {
    try {
      return _chatRooms.firstWhere((room) => room.id == chatRoomId);
    } catch (e) {
      return null;
    }
  }

  /// Check if provider has any chat rooms
  bool get hasChatRooms => _chatRooms.isNotEmpty;

  /// Get sync status information
  Map<String, dynamic> getSyncStatus() {
    return {
      'isInitialized': _isInitialized,
      'isLoading': _isLoading,
      'chatRoomsCount': _chatRooms.length,
      'syncManagerStatus': _syncManager.getSyncStatus(),
    };
  }

  @override
  void dispose() {
    debugPrint('🔄 [CHAT_LIST_PROVIDER] Disposing...');

    _chatRoomsSubscription?.cancel();
    _chatRoomsSubscription = null;

    super.dispose();

    debugPrint('🔄 [CHAT_LIST_PROVIDER] Disposed');
  }
}
