import 'package:flutter/material.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/services/chat_service.dart';
import 'package:tolk/services/encryption_service.dart';
import 'package:tolk/services/sync_manager.dart';
import 'package:tolk/services/local_storage_service.dart';
import 'dart:async';

class ChatProvider extends ChangeNotifier {
  final ChatService _chatService = ChatService();
  final EncryptionService _encryptionService = EncryptionService();
  final SyncManager _syncManager = SyncManager.instance;
  final LocalStorageService _localStorage = LocalStorageService.instance;

  // Chat-specific state
  final Map<String, List<Message>> _chatMessages = {};
  final Map<String, bool> _isLoadingMore = {};
  final Map<String, bool> _hasMoreMessages = {};
  final Map<String, StreamSubscription<List<Message>>?> _messageStreams = {};
  final Map<String, StreamSubscription<ChatRoom>?> _chatRoomStreams = {};
  final Map<String, bool> _chatRoomEncryptionStatus = {};

  static const int _messagesPerPage = 15;
  final bool _isInitialized = false;

  // Getters for specific chat room
  List<Message> getMessages(String chatRoomId) {
    return _chatMessages[chatRoomId] ?? [];
  }

  bool isLoadingMore(String chatRoomId) {
    return _isLoadingMore[chatRoomId] ?? false;
  }

  bool hasMoreMessages(String chatRoomId) {
    return _hasMoreMessages[chatRoomId] ?? true;
  }

  // Initialize chat room
  void initializeChatRoom(String chatRoomId) {
    if (_messageStreams.containsKey(chatRoomId)) {
      return; // Already initialized
    }

    debugPrint('🔄 [CHAT_PROVIDER] Initializing chat room: $chatRoomId');

    // Initialize state
    _chatMessages[chatRoomId] = [];
    _isLoadingMore[chatRoomId] = false;
    _hasMoreMessages[chatRoomId] = true;

    // Pre-load encryption key if needed
    _preloadEncryptionKey(chatRoomId);

    // Start listening to chat room changes (for encryption status)
    _startChatRoomListener(chatRoomId);

    // Start sync manager for this chat room
    _syncManager.startMessageSync(chatRoomId);

    // Listen to sync manager's message stream
    _startSyncManagerMessageStream(chatRoomId);
  }

  // Pre-load encryption key for a chat room if it's encrypted
  Future<void> _preloadEncryptionKey(String chatRoomId) async {
    try {
      debugPrint(
        '🔐 [CHAT_PROVIDER] Pre-loading encryption key for chat room: $chatRoomId',
      );

      // Check if chat room is encrypted and load key if needed
      final chatRoom = await _chatService.getChatRoomById(chatRoomId);
      if (chatRoom?.isEncrypted == true) {
        debugPrint('🔐 [CHAT_PROVIDER] Chat room is encrypted, loading key...');
        final keyLoaded = await _encryptionService.loadChatRoomKey(chatRoomId);

        if (keyLoaded) {
          debugPrint('🔐 [CHAT_PROVIDER] Encryption key loaded successfully');
        } else {
          debugPrint('❌ [CHAT_PROVIDER] Failed to load encryption key');
        }
      } else {
        debugPrint('🔐 [CHAT_PROVIDER] Chat room is not encrypted');
      }
    } catch (e) {
      debugPrint('❌ [CHAT_PROVIDER] Error pre-loading encryption key: $e');
    }
  }

  // Start listening to sync manager's message stream
  void _startSyncManagerMessageStream(String chatRoomId) {
    final messageStream = _syncManager.getMessagesStream(chatRoomId);
    if (messageStream != null) {
      _messageStreams[chatRoomId] = messageStream.listen((messages) {
        debugPrint(
          '🔄 [CHAT_PROVIDER] Received ${messages.length} messages from sync manager for chat room: $chatRoomId',
        );
        _chatMessages[chatRoomId] = messages;
        notifyListeners();
      });
    }
  }

  // Start listening to message stream for a chat room (legacy method - kept for compatibility)
  void _startMessageStream(String chatRoomId) {
    _messageStreams[chatRoomId] = _chatService
        .getMessagesPaginated(chatRoomId, limit: _messagesPerPage)
        .listen((latestMessages) {
          _mergeMessages(chatRoomId, latestMessages);
        });
  }

  // Start listening to chat room changes (for encryption status changes)
  void _startChatRoomListener(String chatRoomId) {
    _chatRoomStreams[chatRoomId] = _chatService
        .getChatRoomStream(chatRoomId)
        .listen((chatRoom) {
          _handleChatRoomChange(chatRoomId, chatRoom);
        });
  }

  // Handle chat room changes (especially encryption status changes)
  void _handleChatRoomChange(String chatRoomId, ChatRoom chatRoom) {
    final previousEncryptionStatus = _chatRoomEncryptionStatus[chatRoomId];
    final currentEncryptionStatus = chatRoom.isEncrypted;

    // Store current encryption status
    _chatRoomEncryptionStatus[chatRoomId] = currentEncryptionStatus;

    // Check if encryption status changed
    if (previousEncryptionStatus != null &&
        previousEncryptionStatus != currentEncryptionStatus) {
      debugPrint(
        '🔐 [CHAT_PROVIDER] Encryption status changed for chat room $chatRoomId: '
        '$previousEncryptionStatus -> $currentEncryptionStatus',
      );

      if (currentEncryptionStatus) {
        // Encryption was enabled - load the encryption key
        debugPrint('🔐 [CHAT_PROVIDER] Encryption enabled, loading key...');
        _preloadEncryptionKey(chatRoomId);
      } else {
        // Encryption was disabled - clear the encryption key
        debugPrint('🔐 [CHAT_PROVIDER] Encryption disabled, clearing key...');
        _clearEncryptionKey(chatRoomId);
      }

      // Refresh messages to apply encryption/decryption changes
      _refreshMessagesForEncryptionChange(chatRoomId);
    }
  }

  // Clear encryption key when encryption is disabled
  void _clearEncryptionKey(String chatRoomId) {
    try {
      _encryptionService.clearChatRoomKey(chatRoomId);
      debugPrint(
        '🔐 [CHAT_PROVIDER] Encryption key cleared for chat room: $chatRoomId',
      );
    } catch (e) {
      debugPrint('❌ [CHAT_PROVIDER] Error clearing encryption key: $e');
    }
  }

  // Refresh messages when encryption status changes
  void _refreshMessagesForEncryptionChange(String chatRoomId) {
    debugPrint(
      '🔐 [CHAT_PROVIDER] Refreshing messages for encryption change...',
    );

    // Cancel existing message stream
    _messageStreams[chatRoomId]?.cancel();

    // Clear existing messages to force reload with new encryption status
    _chatMessages[chatRoomId] = [];
    _isLoadingMore[chatRoomId] = false;
    _hasMoreMessages[chatRoomId] = true;

    // Restart message stream
    _startMessageStream(chatRoomId);

    // Notify listeners to update UI
    notifyListeners();
  }

  // Merge new messages with existing ones
  void _mergeMessages(String chatRoomId, List<Message> latestMessages) {
    if (latestMessages.isEmpty) return;

    final existingMessages = _chatMessages[chatRoomId] ?? [];

    // Create a map of existing messages for quick lookup by ID
    final Map<String, Message> existingMessagesMap = {
      for (var msg in existingMessages) msg.id: msg,
    };

    // Process latest messages: update existing or add new
    for (final latestMsg in latestMessages) {
      if (existingMessagesMap.containsKey(latestMsg.id)) {
        // If message with this ID exists, update it in the map
        existingMessagesMap[latestMsg.id] = latestMsg;
      } else {
        // If it's a new message, add it to the map
        existingMessagesMap[latestMsg.id] = latestMsg;
      }
    }

    // Convert map back to a list and sort by timestamp (newest first for reverse ListView)
    final mergedMessages = existingMessagesMap.values.toList();
    mergedMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    _chatMessages[chatRoomId] = mergedMessages;
    notifyListeners();
  }

  // Load more messages for pagination
  Future<void> loadMoreMessages(String chatRoomId) async {
    if (_isLoadingMore[chatRoomId] == true ||
        _hasMoreMessages[chatRoomId] == false ||
        (_chatMessages[chatRoomId]?.isEmpty ?? true)) {
      return;
    }

    _isLoadingMore[chatRoomId] = true;
    notifyListeners();

    try {
      final currentMessages = _chatMessages[chatRoomId]!;
      final lastMessage = currentMessages.last;

      // Use sync manager to load more messages (it handles local storage + Firestore)
      final moreMessages = await _syncManager.loadMoreMessages(
        chatRoomId,
        lastMessage.timestamp,
        limit: _messagesPerPage,
      );

      if (moreMessages.isNotEmpty) {
        // Add more messages to existing list
        final updatedMessages = List<Message>.from(currentMessages);
        updatedMessages.addAll(moreMessages);
        _chatMessages[chatRoomId] = updatedMessages;
        _hasMoreMessages[chatRoomId] = moreMessages.length == _messagesPerPage;
      } else {
        _hasMoreMessages[chatRoomId] = false;
      }
    } catch (e) {
      debugPrint('Error loading more messages: $e');
    } finally {
      _isLoadingMore[chatRoomId] = false;
      notifyListeners();
    }
  }

  // Add a new message optimistically (for immediate UI update)
  void addMessageOptimistically(String chatRoomId, Message message) {
    // Use sync manager for optimistic updates (handles local storage)
    _syncManager.addMessageOptimistically(chatRoomId, message);

    // Also update local state immediately for UI responsiveness
    final currentMessages = _chatMessages[chatRoomId] ?? [];
    final updatedMessages = [message, ...currentMessages];
    _chatMessages[chatRoomId] = updatedMessages;
    notifyListeners();
  }

  // Update message status (for delivery/read receipts)
  void updateMessageStatus(
    String chatRoomId,
    String messageId,
    MessageStatus status,
  ) {
    final currentMessages = _chatMessages[chatRoomId];
    if (currentMessages == null) return;

    final messageIndex = currentMessages.indexWhere(
      (msg) => msg.id == messageId,
    );
    if (messageIndex == -1) return;

    final updatedMessage = currentMessages[messageIndex].copyWith(
      status: status,
    );
    currentMessages[messageIndex] = updatedMessage;
    _chatMessages[chatRoomId] = List.from(currentMessages);
    notifyListeners();
  }

  // Remove a message (for delete functionality)
  void removeMessage(String chatRoomId, String messageId) {
    final currentMessages = _chatMessages[chatRoomId];
    if (currentMessages == null) return;

    currentMessages.removeWhere((msg) => msg.id == messageId);
    _chatMessages[chatRoomId] = List.from(currentMessages);
    notifyListeners();
  }

  // Refresh chat room (restart message stream to get updated encryption state)
  void refreshChatRoom(String chatRoomId) {
    // Stop sync manager for this chat room
    _syncManager.stopMessageSync(chatRoomId);

    // Cancel existing streams
    _messageStreams[chatRoomId]?.cancel();
    _messageStreams.remove(chatRoomId);
    _chatRoomStreams[chatRoomId]?.cancel();
    _chatRoomStreams.remove(chatRoomId);

    // Clear existing messages to force reload
    _chatMessages[chatRoomId] = [];
    _isLoadingMore[chatRoomId] = false;
    _hasMoreMessages[chatRoomId] = true;

    // Pre-load encryption key (in case encryption settings changed)
    _preloadEncryptionKey(chatRoomId);

    // Restart chat room listener
    _startChatRoomListener(chatRoomId);

    // Restart sync manager
    _syncManager.startMessageSync(chatRoomId);
    _startSyncManagerMessageStream(chatRoomId);

    // Notify listeners to update UI
    notifyListeners();
  }

  // Clean up resources for a chat room
  void disposeChatRoom(String chatRoomId) {
    // Stop sync manager for this chat room
    _syncManager.stopMessageSync(chatRoomId);

    _messageStreams[chatRoomId]?.cancel();
    _messageStreams.remove(chatRoomId);
    _chatRoomStreams[chatRoomId]?.cancel();
    _chatRoomStreams.remove(chatRoomId);
    _chatMessages.remove(chatRoomId);
    _isLoadingMore.remove(chatRoomId);
    _hasMoreMessages.remove(chatRoomId);
    _chatRoomEncryptionStatus.remove(chatRoomId);
  }

  @override
  void dispose() {
    // Cancel all streams
    for (final subscription in _messageStreams.values) {
      subscription?.cancel();
    }
    for (final subscription in _chatRoomStreams.values) {
      subscription?.cancel();
    }
    _messageStreams.clear();
    _chatRoomStreams.clear();
    _chatMessages.clear();
    _isLoadingMore.clear();
    _hasMoreMessages.clear();
    _chatRoomEncryptionStatus.clear();
    super.dispose();
  }
}
