// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_storage_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalMessageAdapter extends TypeAdapter<LocalMessage> {
  @override
  final int typeId = 0;

  @override
  LocalMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalMessage(
      id: fields[0] as String,
      chatRoomId: fields[1] as String,
      senderId: fields[2] as String,
      text: fields[3] as String?,
      mediaUrl: fields[4] as String?,
      type: fields[5] as int,
      status: fields[6] as int,
      timestamp: fields[7] as DateTime,
      readBy: (fields[8] as List).cast<String>(),
      metadata: (fields[9] as Map?)?.cast<String, dynamic>(),
      isEncrypted: fields[10] as bool,
      encryptedText: fields[11] as String?,
      encryptedMediaUrl: fields[12] as String?,
      lastUpdated: fields[13] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, LocalMessage obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.chatRoomId)
      ..writeByte(2)
      ..write(obj.senderId)
      ..writeByte(3)
      ..write(obj.text)
      ..writeByte(4)
      ..write(obj.mediaUrl)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.timestamp)
      ..writeByte(8)
      ..write(obj.readBy)
      ..writeByte(9)
      ..write(obj.metadata)
      ..writeByte(10)
      ..write(obj.isEncrypted)
      ..writeByte(11)
      ..write(obj.encryptedText)
      ..writeByte(12)
      ..write(obj.encryptedMediaUrl)
      ..writeByte(13)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LocalChatRoomAdapter extends TypeAdapter<LocalChatRoom> {
  @override
  final int typeId = 1;

  @override
  LocalChatRoom read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalChatRoom(
      id: fields[0] as String,
      participants: (fields[1] as List).cast<String>(),
      isGroupChat: fields[2] as bool,
      groupName: fields[3] as String?,
      groupImage: fields[4] as String?,
      lastMessage: fields[5] as String?,
      lastMessageTime: fields[6] as DateTime?,
      unreadCount: (fields[7] as Map).cast<String, int>(),
      createdAt: fields[8] as DateTime,
      createdBy: fields[9] as String,
      isEncrypted: fields[10] as bool,
      encryptionEnabledAt: fields[11] as DateTime?,
      encryptionEnabledBy: fields[12] as String?,
      encryptionDisabledAt: fields[13] as DateTime?,
      encryptionDisabledBy: fields[14] as String?,
      lastUpdated: fields[15] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, LocalChatRoom obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.participants)
      ..writeByte(2)
      ..write(obj.isGroupChat)
      ..writeByte(3)
      ..write(obj.groupName)
      ..writeByte(4)
      ..write(obj.groupImage)
      ..writeByte(5)
      ..write(obj.lastMessage)
      ..writeByte(6)
      ..write(obj.lastMessageTime)
      ..writeByte(7)
      ..write(obj.unreadCount)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.createdBy)
      ..writeByte(10)
      ..write(obj.isEncrypted)
      ..writeByte(11)
      ..write(obj.encryptionEnabledAt)
      ..writeByte(12)
      ..write(obj.encryptionEnabledBy)
      ..writeByte(13)
      ..write(obj.encryptionDisabledAt)
      ..writeByte(14)
      ..write(obj.encryptionDisabledBy)
      ..writeByte(15)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalChatRoomAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LocalTranslationAdapter extends TypeAdapter<LocalTranslation> {
  @override
  final int typeId = 2;

  @override
  LocalTranslation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalTranslation(
      originalText: fields[0] as String,
      translatedText: fields[1] as String,
      targetLanguage: fields[2] as String,
      sourceLanguage: fields[3] as String?,
      createdAt: fields[4] as DateTime,
      chatRoomId: fields[5] as String,
    );
  }

  @override
  void write(BinaryWriter writer, LocalTranslation obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.originalText)
      ..writeByte(1)
      ..write(obj.translatedText)
      ..writeByte(2)
      ..write(obj.targetLanguage)
      ..writeByte(3)
      ..write(obj.sourceLanguage)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.chatRoomId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalTranslationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
