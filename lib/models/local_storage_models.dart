import 'package:hive/hive.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'chat_models.dart';

part 'local_storage_models.g.dart';

@HiveType(typeId: 0)
class LocalMessage extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String chatRoomId;

  @HiveField(2)
  final String senderId;

  @HiveField(3)
  final String? text;

  @HiveField(4)
  final String? mediaUrl;

  @HiveField(5)
  final int type; // MessageType index

  @HiveField(6)
  final int status; // MessageStatus index

  @HiveField(7)
  final DateTime timestamp;

  @HiveField(8)
  final List<String> readBy;

  @HiveField(9)
  final Map<String, dynamic>? metadata;

  @HiveField(10)
  final bool isEncrypted;

  @HiveField(11)
  final String? encryptedText;

  @HiveField(12)
  final String? encryptedMediaUrl;

  @HiveField(13)
  final DateTime lastUpdated; // For sync tracking

  LocalMessage({
    required this.id,
    required this.chatRoomId,
    required this.senderId,
    this.text,
    this.mediaUrl,
    required this.type,
    required this.status,
    required this.timestamp,
    required this.readBy,
    this.metadata,
    this.isEncrypted = false,
    this.encryptedText,
    this.encryptedMediaUrl,
    required this.lastUpdated,
  });

  // Convert from Message to LocalMessage
  factory LocalMessage.fromMessage(Message message) {
    return LocalMessage(
      id: message.id,
      chatRoomId: message.chatRoomId,
      senderId: message.senderId,
      text: message.text,
      mediaUrl: message.mediaUrl,
      type: message.type.index,
      status: message.status.index,
      timestamp: message.timestamp,
      readBy: List<String>.from(message.readBy),
      metadata: message.metadata != null ? Map<String, dynamic>.from(message.metadata!) : null,
      isEncrypted: message.isEncrypted,
      encryptedText: message.encryptedText,
      encryptedMediaUrl: message.encryptedMediaUrl,
      lastUpdated: DateTime.now(),
    );
  }

  // Convert to Message
  Message toMessage() {
    return Message(
      id: id,
      chatRoomId: chatRoomId,
      senderId: senderId,
      text: text,
      mediaUrl: mediaUrl,
      type: MessageType.values[type],
      status: MessageStatus.values[status],
      timestamp: timestamp,
      readBy: List<String>.from(readBy),
      metadata: metadata != null ? Map<String, dynamic>.from(metadata!) : null,
      isEncrypted: isEncrypted,
      encryptedText: encryptedText,
      encryptedMediaUrl: encryptedMediaUrl,
    );
  }

  LocalMessage copyWith({
    int? status,
    List<String>? readBy,
    bool? isEncrypted,
    String? encryptedText,
    String? encryptedMediaUrl,
    DateTime? lastUpdated,
  }) {
    return LocalMessage(
      id: id,
      chatRoomId: chatRoomId,
      senderId: senderId,
      text: text,
      mediaUrl: mediaUrl,
      type: type,
      status: status ?? this.status,
      timestamp: timestamp,
      readBy: readBy ?? List<String>.from(this.readBy),
      metadata: metadata,
      isEncrypted: isEncrypted ?? this.isEncrypted,
      encryptedText: encryptedText ?? this.encryptedText,
      encryptedMediaUrl: encryptedMediaUrl ?? this.encryptedMediaUrl,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }
}

@HiveType(typeId: 1)
class LocalChatRoom extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final List<String> participants;

  @HiveField(2)
  final bool isGroupChat;

  @HiveField(3)
  final String? groupName;

  @HiveField(4)
  final String? groupImage;

  @HiveField(5)
  final String? lastMessage;

  @HiveField(6)
  final DateTime? lastMessageTime;

  @HiveField(7)
  final Map<String, int> unreadCount;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  final String createdBy;

  @HiveField(10)
  final bool isEncrypted;

  @HiveField(11)
  final DateTime? encryptionEnabledAt;

  @HiveField(12)
  final String? encryptionEnabledBy;

  @HiveField(13)
  final DateTime? encryptionDisabledAt;

  @HiveField(14)
  final String? encryptionDisabledBy;

  @HiveField(15)
  final DateTime lastUpdated; // For sync tracking

  LocalChatRoom({
    required this.id,
    required this.participants,
    this.isGroupChat = false,
    this.groupName,
    this.groupImage,
    this.lastMessage,
    this.lastMessageTime,
    required this.unreadCount,
    required this.createdAt,
    required this.createdBy,
    this.isEncrypted = false,
    this.encryptionEnabledAt,
    this.encryptionEnabledBy,
    this.encryptionDisabledAt,
    this.encryptionDisabledBy,
    required this.lastUpdated,
  });

  // Convert from ChatRoom to LocalChatRoom
  factory LocalChatRoom.fromChatRoom(ChatRoom chatRoom) {
    return LocalChatRoom(
      id: chatRoom.id,
      participants: List<String>.from(chatRoom.participants),
      isGroupChat: chatRoom.isGroupChat,
      groupName: chatRoom.groupName,
      groupImage: chatRoom.groupImage,
      lastMessage: chatRoom.lastMessage,
      lastMessageTime: chatRoom.lastMessageTime,
      unreadCount: Map<String, int>.from(chatRoom.unreadCount),
      createdAt: chatRoom.createdAt,
      createdBy: chatRoom.createdBy,
      isEncrypted: chatRoom.isEncrypted,
      encryptionEnabledAt: chatRoom.encryptionEnabledAt,
      encryptionEnabledBy: chatRoom.encryptionEnabledBy,
      encryptionDisabledAt: chatRoom.encryptionDisabledAt,
      encryptionDisabledBy: chatRoom.encryptionDisabledBy,
      lastUpdated: DateTime.now(),
    );
  }

  // Convert to ChatRoom
  ChatRoom toChatRoom() {
    return ChatRoom(
      id: id,
      participants: List<String>.from(participants),
      isGroupChat: isGroupChat,
      groupName: groupName,
      groupImage: groupImage,
      lastMessage: lastMessage,
      lastMessageTime: lastMessageTime,
      unreadCount: Map<String, int>.from(unreadCount),
      createdAt: createdAt,
      createdBy: createdBy,
      isEncrypted: isEncrypted,
      encryptionEnabledAt: encryptionEnabledAt,
      encryptionEnabledBy: encryptionEnabledBy,
      encryptionDisabledAt: encryptionDisabledAt,
      encryptionDisabledBy: encryptionDisabledBy,
    );
  }

  LocalChatRoom copyWith({
    String? lastMessage,
    DateTime? lastMessageTime,
    Map<String, int>? unreadCount,
    bool? isEncrypted,
    DateTime? encryptionEnabledAt,
    String? encryptionEnabledBy,
    DateTime? encryptionDisabledAt,
    String? encryptionDisabledBy,
    DateTime? lastUpdated,
  }) {
    return LocalChatRoom(
      id: id,
      participants: List<String>.from(participants),
      isGroupChat: isGroupChat,
      groupName: groupName,
      groupImage: groupImage,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? Map<String, int>.from(this.unreadCount),
      createdAt: createdAt,
      createdBy: createdBy,
      isEncrypted: isEncrypted ?? this.isEncrypted,
      encryptionEnabledAt: encryptionEnabledAt ?? this.encryptionEnabledAt,
      encryptionEnabledBy: encryptionEnabledBy ?? this.encryptionEnabledBy,
      encryptionDisabledAt: encryptionDisabledAt ?? this.encryptionDisabledAt,
      encryptionDisabledBy: encryptionDisabledBy ?? this.encryptionDisabledBy,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }
}

@HiveType(typeId: 2)
class LocalTranslation extends HiveObject {
  @HiveField(0)
  final String originalText;

  @HiveField(1)
  final String translatedText;

  @HiveField(2)
  final String targetLanguage;

  @HiveField(3)
  final String? sourceLanguage;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final String chatRoomId; // For context-specific translations

  LocalTranslation({
    required this.originalText,
    required this.translatedText,
    required this.targetLanguage,
    this.sourceLanguage,
    required this.createdAt,
    required this.chatRoomId,
  });

  String get cacheKey => '${chatRoomId}_${targetLanguage}_${originalText.hashCode}';
}
